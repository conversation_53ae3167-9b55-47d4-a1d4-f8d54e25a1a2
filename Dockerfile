# Multi-stage Dockerfile for Next.js with runtime environment variable injection

# Stage 1: Dependencies
FROM node:20-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files
COPY package*.json ./
# Use npm install instead of npm ci for production dependencies
# This handles cases where package-lock.json might be out of sync
# Skip prepare script to avoid Husky issues in production build
RUN npm install --omit=dev --legacy-peer-deps --ignore-scripts

# Stage 2: Builder
FROM node:20-alpine AS builder
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files and install all dependencies (including dev)
COPY package*.json ./
# Use npm install to handle lock file sync issues
# Skip scripts during initial install to avoid issues
RUN npm install --legacy-peer-deps --ignore-scripts
# Run prepare script after install (this includes Husky setup)
RUN npm run prepare || true

# Copy source code
COPY . .

# Build arguments for NEXT_PUBLIC variables
# These will be replaced at runtime by the entrypoint script
ARG NEXT_PUBLIC_AUTH_API_BASE_URL=RUNTIME_NEXT_PUBLIC_AUTH_API_BASE_URL
ARG NEXT_PUBLIC_WEBSOCKET_URL=RUNTIME_NEXT_PUBLIC_WEBSOCKET_URL
ARG NEXT_PUBLIC_SERVICES_BASE_URL=RUNTIME_NEXT_PUBLIC_SERVICES_BASE_URL
ARG NEXT_PUBLIC_AUTH_SERVICE_URL=RUNTIME_NEXT_PUBLIC_AUTH_SERVICE_URL
ARG NEXT_PUBLIC_OAUTH_CLIENT_ID=RUNTIME_NEXT_PUBLIC_OAUTH_CLIENT_ID
ARG NEXT_PUBLIC_OAUTH_REDIRECT_URL=RUNTIME_NEXT_PUBLIC_OAUTH_REDIRECT_URL

# Set build-time environment variables
ENV NEXT_PUBLIC_AUTH_API_BASE_URL=$NEXT_PUBLIC_AUTH_API_BASE_URL
ENV NEXT_PUBLIC_WEBSOCKET_URL=$NEXT_PUBLIC_WEBSOCKET_URL
ENV NEXT_PUBLIC_SERVICES_BASE_URL=$NEXT_PUBLIC_SERVICES_BASE_URL
ENV NEXT_PUBLIC_AUTH_SERVICE_URL=$NEXT_PUBLIC_AUTH_SERVICE_URL
ENV NEXT_PUBLIC_OAUTH_CLIENT_ID=$NEXT_PUBLIC_OAUTH_CLIENT_ID
ENV NEXT_PUBLIC_OAUTH_REDIRECT_URL=$NEXT_PUBLIC_OAUTH_REDIRECT_URL

# Build the application
RUN npm run build

# Stage 3: Runner
FROM node:20-alpine AS runner
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Set production environment
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Copy necessary files from builder
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# Copy entrypoint script
COPY --from=builder /app/docker-entrypoint.sh ./
RUN chmod +x ./docker-entrypoint.sh

# Change ownership to nextjs user
RUN chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Set the port
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Use entrypoint script to inject runtime environment variables
ENTRYPOINT ["./docker-entrypoint.sh"]
CMD ["node", "server.js"]