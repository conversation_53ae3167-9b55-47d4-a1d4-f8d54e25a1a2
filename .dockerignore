# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp
.pnp.js

# Testing
coverage
.nyc_output

# Next.js
.next
out
build
dist

# Environment files
.env
.env.local
.env.production.local
.env.development.local
.env.test.local

# IDE
.vscode
.idea
*.swp
*.swo
*~
.DS_Store

# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
docs

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# CI/CD
.github
.gitlab-ci.yml
.circleci

# Temporary files
tmp
temp
*.tmp
*.temp

# Logs
logs
*.log

# OS files
Thumbs.db

# Development files
.eslintcache
.cache