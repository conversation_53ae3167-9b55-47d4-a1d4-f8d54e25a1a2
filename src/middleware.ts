import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Middleware for handling authentication, logging, and other cross-cutting concerns
 */
export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;
  
  // Add request logging
  console.log(`[${new Date().toISOString()}] ${request.method} ${path}`);
  
  // Add response headers
  const response = NextResponse.next();
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  // Example of how to handle authentication
  // This is just a placeholder - in a real app, you would check for a valid session
  // const isAuthenticated = request.cookies.has('auth-token');
  // const isAuthRoute = path.startsWith('/api') || path.startsWith('/dashboard');
  
  // if (isAuthRoute && !isAuthenticated) {
  //   return NextResponse.redirect(new URL('/login', request.url));
  // }
  
  return response;
}

/**
 * Configure which paths should be processed by the middleware
 */
export const config = {
  matcher: [
    // Apply to all routes except static files, api routes, and _next
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
