"use client";

import { createContext, useContext, useState, ReactNode, useEffect } from "react";

interface SessionContextType {
  currentSessionId: string | null;
  setCurrentSessionId: (sessionId: string | null) => void;
  currentEscalationSessionId: string | null;
  setCurrentEscalationSessionId: (sessionId: string | null) => void;
  clearSession: () => void;
  clearEscalation: () => void;
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

export function SessionProvider({ children }: { children: ReactNode }) {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [currentEscalationSessionId, setCurrentEscalationSessionId] = useState<string | null>(null);

  const clearSession = () => {
    setCurrentSessionId(null);
  };

  const clearEscalation = () => {
    setCurrentEscalationSessionId(null);
  };

  // Listen for escalation resolution events
  useEffect(() => {
    const handleEscalationResolved = (event: CustomEvent) => {
      const resolvedEscalationId = event.detail?.escalationId;
      const resolvedSessionId = event.detail?.sessionId;

      // If the currently active escalation was resolved, clear it
      if (resolvedSessionId && currentEscalationSessionId === resolvedSessionId) {
        console.log("🔄 Current escalation was resolved, clearing escalation session");
        setCurrentEscalationSessionId(null);
      }
    };

    // Listen for custom escalation resolution events
    window.addEventListener("escalation-resolved", handleEscalationResolved as EventListener);

    return () => {
      window.removeEventListener("escalation-resolved", handleEscalationResolved as EventListener);
    };
  }, [currentEscalationSessionId]);

  return (
    <SessionContext.Provider
      value={{
        currentSessionId,
        setCurrentSessionId,
        currentEscalationSessionId,
        setCurrentEscalationSessionId,
        clearSession,
        clearEscalation,
      }}
    >
      {children}
    </SessionContext.Provider>
  );
}

export function useSession() {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error("useSession must be used within a SessionProvider");
  }
  return context;
}
