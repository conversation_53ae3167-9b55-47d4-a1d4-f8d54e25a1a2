"use client";

import { createContext, useContext, useState, ReactNode } from "react";

interface SessionContextType {
  currentSessionId: string | null;
  setCurrentSessionId: (sessionId: string | null) => void;
  currentEscalationSessionId: string | null;
  setCurrentEscalationSessionId: (sessionId: string | null) => void;
  clearSession: () => void;
  clearEscalation: () => void;
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

export function SessionProvider({ children }: { children: ReactNode }) {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [currentEscalationSessionId, setCurrentEscalationSessionId] = useState<string | null>(null);

  const clearSession = () => {
    setCurrentSessionId(null);
  };

  const clearEscalation = () => {
    setCurrentEscalationSessionId(null);
  };

  return (
    <SessionContext.Provider
      value={{
        currentSessionId,
        setCurrentSessionId,
        currentEscalationSessionId,
        setCurrentEscalationSessionId,
        clearSession,
        clearEscalation,
      }}
    >
      {children}
    </SessionContext.Provider>
  );
}

export function useSession() {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error("useSession must be used within a SessionProvider");
  }
  return context;
}
