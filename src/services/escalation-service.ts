import { api } from "@/lib/api-client";
import { Escalation, EscalationListResponse, ApiResponse, EscalationFilters, UpdateEscalationRequest, CreateEscalationRequest } from "@/types/escalation";

class EscalationService {
  private baseUrl = "/v1/escalations";

  /**
   * Get list of escalations with optional filtering
   */
  async getEscalations(filters?: EscalationFilters): Promise<EscalationListResponse> {
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          params.append(key, value.toString());
        }
      });
    }

    const url = params.toString() ? `${this.baseUrl}?${params.toString()}` : this.baseUrl;

    const response = await api.get<ApiResponse<EscalationListResponse>>(url, { service: "escalation" });
    return response.data;
  }

  /**
   * Get escalations assigned to a specific operator
   */
  async getMyEscalations(operatorId: string, filters?: Omit<EscalationFilters, "assigned_operator_id">): Promise<EscalationListResponse> {
    return this.getEscalations({
      ...filters,
      assigned_operator_id: operatorId,
    });
  }

  /**
   * Get escalation by ID
   */
  async getEscalationById(id: string): Promise<Escalation> {
    const response = await api.get<ApiResponse<{ escalation: Escalation }>>(`${this.baseUrl}/${id}`, { service: "escalation" });
    return response.data.escalation;
  }

  /**
   * Update escalation status or assignment
   */
  async updateEscalation(id: string, updates: UpdateEscalationRequest): Promise<Escalation> {
    const response = await api.put<ApiResponse<{ escalation: Escalation }>>(`${this.baseUrl}/${id}`, updates, { service: "escalation" });
    return response.data.escalation;
  }

  /**
   * Assign escalation to operator
   */
  async assignEscalation(id: string, operatorId: string): Promise<Escalation> {
    const response = await api.post<ApiResponse<{ escalation: Escalation }>>(
      `${this.baseUrl}/${id}/assign`,
      {
        assigned_operator_id: operatorId,
        operator_user_type: "AGENT",
        updated_by: operatorId,
      },
      { service: "escalation" }
    );
    return response.data.escalation;
  }

  /**
   * Resolve escalation
   */
  async resolveEscalation(id: string, operatorId: string): Promise<Escalation> {
    const response = await api.post<ApiResponse<{ escalation: Escalation }>>(
      `${this.baseUrl}/${id}/resolve`,
      {
        updated_by: operatorId,
      },
      { service: "escalation" }
    );
    return response.data.escalation;
  }

  /**
   * Create new escalation
   */
  async createEscalation(escalation: CreateEscalationRequest): Promise<Escalation> {
    const response = await api.post<ApiResponse<{ escalation: Escalation }>>(this.baseUrl, escalation, { service: "escalation" });
    return response.data.escalation;
  }

  /**
   * Get escalations by session ID
   */
  async getEscalationsBySession(sessionId: string): Promise<Escalation[]> {
    const response = await api.get<ApiResponse<EscalationListResponse>>(`${this.baseUrl}?session_id=${sessionId}`, { service: "escalation" });
    return response.data.escalations;
  }

  /**
   * Get pending escalations for queue display
   */
  async getPendingEscalations(page = 1, pageSize = 10): Promise<EscalationListResponse> {
    return this.getEscalations({
      status: "pending",
      page,
      pageSize,
    });
  }

  /**
   * Parse escalation meta data
   */
  parseEscalationMeta(meta: string): Record<string, unknown> {
    try {
      return JSON.parse(meta || "{}");
    } catch (error) {
      console.error("Failed to parse escalation meta:", error);
      return {};
    }
  }

  /**
   * Get priority from meta data
   */
  getPriority(escalation: Escalation): string {
    const meta = this.parseEscalationMeta(escalation.meta);
    return (meta.priority as string) || "medium";
  }

  /**
   * Get priority color for UI
   */
  getPriorityColor(priority: string): string {
    switch (priority.toLowerCase()) {
      case "urgent":
        return "text-red-600 bg-red-50 border-red-200";
      case "high":
        return "text-orange-600 bg-orange-50 border-orange-200";
      case "medium":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "low":
        return "text-green-600 bg-green-50 border-green-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  }

  /**
   * Get status color for UI
   */
  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case "pending":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "assigned":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "resolved":
        return "text-green-600 bg-green-50 border-green-200";
      case "cancelled":
        return "text-gray-600 bg-gray-50 border-gray-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  }
}

export const escalationService = new EscalationService();
