/**
 * API service for interacting with backend services
 */

// Base API URL - should be configured in environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_AUTH_API_BASE_URL || 'https://k2-api.klubworks.com';

/**
 * Get a guest UUID from the server
 * This ensures UUIDs are generated and tracked server-side
 */
export async function getGuestUuid(): Promise<string> {
  try {
    const response = await fetch(`${API_BASE_URL}/guest/uuid`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to get guest UUID: ${response.status}`);
    }

    const data = await response.json();
    return data.uuid;
  } catch (error) {
    console.error('Error getting guest UUID from API:', error);
    // Fallback to client-side UUID generation in case of API failure
    return generateFallbackUuid();
  }
}

/**
 * Generate a fallback UUID on the client side
 * Only used when the API call fails
 */
function generateFallbackUuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
