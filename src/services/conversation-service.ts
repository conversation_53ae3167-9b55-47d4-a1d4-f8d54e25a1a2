import { api } from "@/lib/api-client";

export interface Conversation {
  id: string;
  session_id: string;
  user_id?: string;
  sender_type: "USER" | "OPERATOR" | "SYSTEM" | "demand" | "operator";
  sender_id?: string;
  message_id?: string;
  message_type: string;
  message?: string;
  payload?: string; // JSON string
  meta?: string; // JSON string
  created_at: string;
  is_archived?: boolean;
}

export interface ConversationResponse {
  conversation: Conversation;
}

export interface ConversationListResponse {
  conversations: Conversation[];
  total_count: number;
}

export interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
  status: "success" | "error";
}

export interface ConversationFilters {
  session_id?: string;
  user_id?: string;
  sender_type?: string;
  message_type?: string;
  page?: number;
  pageSize?: number;
}

export interface CreateConversationRequest {
  session_id: string;
  user_id?: string;
  sender_type: "customer" | "agent" | "system";
  sender_id?: string;
  message_id?: string;
  message_type: string;
  message?: string;
  payload?: string;
  meta?: string;
}

export interface UpdateConversationRequest {
  session_id?: string;
  user_id?: string;
  sender_type?: "customer" | "agent" | "system";
  sender_id?: string;
  message_id?: string;
  message_type?: string;
  message?: string;
  payload?: string;
  meta?: string;
}

class ConversationService {
  private baseUrl = "/v1/conversations";

  /**
   * Get conversations with optional filtering
   */
  async getConversations(filters?: ConversationFilters): Promise<ConversationListResponse> {
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          params.append(key, value.toString());
        }
      });
    }

    const url = params.toString() ? `${this.baseUrl}?${params.toString()}` : this.baseUrl;

    const response = await api.get<ApiResponse<ConversationListResponse>>(url, { service: "conversation" });
    return response.data;
  }

  /**
   * Get conversation by ID
   */
  async getConversationById(id: string): Promise<Conversation> {
    const response = await api.get<ApiResponse<ConversationResponse>>(`${this.baseUrl}/${id}`, { service: "conversation" });
    return response.data.conversation;
  }

  /**
   * Get conversations by session ID using the direct API endpoint
   */
  async getConversationsBySession(sessionId: string, page = 1, pageSize = 50): Promise<ConversationListResponse> {
    const url = new URL(`${process.env.NEXT_PUBLIC_SERVICES_BASE_URL}/api/conversation-svc/v1/conversations`);
    url.searchParams.set("session_id", sessionId);
    url.searchParams.set("page", page.toString());
    url.searchParams.set("pageSize", pageSize.toString());

    console.log("Fetching conversations from:", url.toString());

    const response = await fetch(url.toString(), {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch conversations: ${response.status}`);
    }

    const data: ApiResponse<ConversationListResponse> = await response.json();

    if (data.code !== 200 || data.status !== "success") {
      throw new Error(`API error: ${data.message}`);
    }

    return data.data;
  }

  /**
   * Create new conversation
   */
  async createConversation(conversation: CreateConversationRequest): Promise<Conversation> {
    const response = await api.post<ApiResponse<ConversationResponse>>(this.baseUrl, conversation, { service: "conversation" });
    return response.data.conversation;
  }

  /**
   * Update conversation
   */
  async updateConversation(id: string, updates: UpdateConversationRequest): Promise<Conversation> {
    const response = await api.put<ApiResponse<ConversationResponse>>(`${this.baseUrl}/${id}`, updates, { service: "conversation" });
    return response.data.conversation;
  }

  /**
   * Delete conversation
   */
  async deleteConversation(id: string): Promise<void> {
    await api.delete(`${this.baseUrl}/${id}`, { service: "conversation" });
  }

  /**
   * Parse conversation payload
   */
  parsePayload(payload?: string): Record<string, unknown> {
    try {
      return JSON.parse(payload || "{}");
    } catch (error) {
      console.error("Failed to parse conversation payload:", error);
      return {};
    }
  }

  /**
   * Parse conversation meta
   */
  parseMeta(meta?: string): Record<string, unknown> {
    try {
      return JSON.parse(meta || "{}");
    } catch (error) {
      console.error("Failed to parse conversation meta:", error);
      return {};
    }
  }

  /**
   * Format conversation for display
   */
  formatConversationForChat(conversation: Conversation) {
    return {
      id: conversation.id,
      role: conversation.sender_type === "USER" ? ("user" as const) : ("assistant" as const),
      content: conversation.message || "",
      timestamp: new Date(conversation.created_at),
      type: conversation.message_type,
      payload: this.parsePayload(conversation.payload),
      meta: this.parseMeta(conversation.meta),
      sender_id: conversation.sender_id,
      message_id: conversation.message_id,
    };
  }

  /**
   * Get conversation history for a session formatted for chat interface
   */
  async getChatHistory(sessionId: string) {
    const response = await this.getConversationsBySession(sessionId);
    return response.conversations.map((conv) => this.formatConversationForChat(conv));
  }
}

export const conversationService = new ConversationService();
