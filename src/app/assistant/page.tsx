"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { ChatInterface } from "@/components/assistant/chat-interface";
import { useAuth } from "@/hooks/use-auth";
import { useSession } from "@/contexts/session-context";

export default function AssistantPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const { currentSessionId, currentEscalationSessionId, clearSession, clearEscalation } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/login");
    }
  }, [isAuthenticated, isLoading, router]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading...</h1>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        </div>
      </div>
    );
  }

  // Only render if authenticated
  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return (
    <div className="flex flex-col h-full bg-white">
      <div
        className="flex-shrink-0 p-4 flex justify-between items-center"
        style={{
          background: "linear-gradient(180deg, #FFFFFF 77.19%, rgba(255, 255, 255, 0.4) 136.87%)",
          height: "80px",
        }}
      >
        <div className="flex items-center mx-auto" style={{ width: "870px" }}>
          <h1 className="text-2xl font-bold">Klub AI Operator</h1>
          <span className="ml-2 px-1 py-0.5 text-xs bg-[#F4F8AC] rounded text-black">Operator</span>
        </div>
      </div>
      <div className="flex-grow overflow-hidden">
        <ChatInterface sessionId={currentSessionId || undefined} escalationSessionId={currentEscalationSessionId || undefined} onBackToChat={clearSession} onBackFromEscalation={clearEscalation} />
      </div>
    </div>
  );
}
