import type { <PERSON>ada<PERSON> } from "next";
import { Inter, D<PERSON>_Sans } from "next/font/google";
import "./globals.css";
import { Sidebar } from "@/components/layout/sidebar";
import { AuthProvider } from "@/hooks/use-auth";
import { SessionProvider } from "@/contexts/session-context";

const inter = Inter({ subsets: ["latin"] });
const dmSans = DM_Sans({
  subsets: ["latin"],
  weight: ["400", "200", "500", "300"],
  variable: "--font-dm-sans",
});

export const metadata: Metadata = {
  title: "Klub AI Operator - SME Lending",
  description: "Operator interface for Klub AI conversational lending platform",
  icons: {
    icon: [
      { url: "/favicon.ico", sizes: "any" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
      </head>
      <body className={`${inter.className} ${dmSans.variable} overflow-hidden`}>
        <AuthProvider>
          <SessionProvider>
            <div className="flex h-screen overflow-hidden">
              <Sidebar />
              <main className="flex-1 flex flex-col overflow-hidden">{children}</main>
            </div>
          </SessionProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
