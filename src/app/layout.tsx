import type { Metada<PERSON> } from "next";
import { Inter, DM_Sans } from "next/font/google";
import "./globals.css";
import { Sidebar } from "@/components/layout/sidebar";
import { AuthProvider } from "@/hooks/use-auth";
import { SessionProvider } from "@/contexts/session-context";

const inter = Inter({ subsets: ["latin"] });
const dmSans = DM_Sans({
  subsets: ["latin"],
  weight: ["400", "200", "500", "300"],
  variable: "--font-dm-sans",
});

export const metadata: Metadata = {
  title: "Klub AI Operator - SME Lending",
  description: "Operator interface for Klub AI conversational lending platform",
  icons: {
    icon: "/favicon.ico",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} ${dmSans.variable} overflow-hidden`}>
        <AuthProvider>
          <SessionProvider>
            <div className="flex h-screen overflow-hidden relative">
              <Sidebar />
              <main className="flex-1 flex flex-col overflow-hidden relative z-10">{children}</main>
            </div>
          </SessionProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
