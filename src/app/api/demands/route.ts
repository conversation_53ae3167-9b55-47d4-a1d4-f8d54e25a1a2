import { NextRequest, NextResponse } from 'next/server';
import { DemandData } from '@/types';

// Mock data for demonstration
const mockDemands: DemandData[] = [
  {
    id: '1',
    title: 'New Marketing Campaign',
    description: 'Launch a new marketing campaign for Q3',
    status: 'pending',
    createdAt: '2025-03-15T10:00:00Z',
    updatedAt: '2025-03-15T10:00:00Z',
    createdBy: 'user1',
  },
  {
    id: '2',
    title: 'Website Redesign',
    description: 'Redesign the company website with new branding',
    status: 'approved',
    createdAt: '2025-03-10T09:30:00Z',
    updatedAt: '2025-03-12T14:20:00Z',
    createdBy: 'user2',
  },
  {
    id: '3',
    title: 'Product Feature Enhancement',
    description: 'Add new features to the product based on customer feedback',
    status: 'rejected',
    createdAt: '2025-03-05T11:45:00Z',
    updatedAt: '2025-03-07T16:30:00Z',
    createdBy: 'user3',
  },
];

/**
 * GET handler for fetching demands with pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    
    const paginatedDemands = mockDemands.slice(startIndex, endIndex);
    
    return NextResponse.json({
      data: {
        data: paginatedDemands,
        total: mockDemands.length,
        page,
        limit,
        totalPages: Math.ceil(mockDemands.length / limit),
      },
      status: 200,
      message: 'Demands retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching demands:', error);
    return NextResponse.json(
      {
        data: null,
        status: 500,
        message: 'Internal server error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a new demand
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.title || !body.description || !body.createdBy) {
      return NextResponse.json(
        {
          data: null,
          status: 400,
          message: 'Missing required fields',
        },
        { status: 400 }
      );
    }
    
    // Create new demand
    const newDemand: DemandData = {
      id: (mockDemands.length + 1).toString(),
      title: body.title,
      description: body.description,
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: body.createdBy,
    };
    
    // In a real app, you would save this to a database
    // For now, we'll just return the new demand
    
    return NextResponse.json(
      {
        data: newDemand,
        status: 201,
        message: 'Demand created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating demand:', error);
    return NextResponse.json(
      {
        data: null,
        status: 500,
        message: 'Internal server error',
      },
      { status: 500 }
    );
  }
}
