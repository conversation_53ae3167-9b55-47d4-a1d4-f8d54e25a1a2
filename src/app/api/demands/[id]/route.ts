import { NextRequest, NextResponse } from 'next/server';

// Mock data for demands
const mockDemands = [
  {
    id: '1',
    businessName: 'Acme Corp',
    amount: 500000,
    purpose: 'Working Capital',
    status: 'Approved',
    createdAt: '2023-04-01T10:30:00Z',
  },
  {
    id: '2',
    businessName: 'TechStart Inc',
    amount: 250000,
    purpose: 'Inventory Purchase',
    status: 'Pending',
    createdAt: '2023-04-05T14:20:00Z',
  },
  {
    id: '3',
    businessName: 'Global Traders',
    amount: 750000,
    purpose: 'Equipment Purchase',
    status: 'Rejected',
    createdAt: '2023-04-10T09:15:00Z',
  },
];

/**
 * GET handler for retrieving a specific demand by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // Find the demand with the matching ID
    const demand = mockDemands.find((d) => d.id === id);

    // If no demand is found, return a 404 response
    if (!demand) {
      return NextResponse.json({ error: 'Demand not found' }, { status: 404 });
    }

    // Return the demand data
    return NextResponse.json(demand);
  } catch (error) {
    console.error('Error fetching demand:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PUT handler for updating a specific demand by ID
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // Find the index of the demand with the matching ID
    const demandIndex = mockDemands.findIndex((d) => d.id === id);

    // If no demand is found, return a 404 response
    if (demandIndex === -1) {
      return NextResponse.json({ error: 'Demand not found' }, { status: 404 });
    }

    // Parse the request body
    const updatedData = await request.json();

    // Update the demand (in a real app, this would update a database)
    const updatedDemand = {
      ...mockDemands[demandIndex],
      ...updatedData,
    };

    // In a real app, you would save this to the database
    mockDemands[demandIndex] = updatedDemand;

    // Return the updated demand
    return NextResponse.json(updatedDemand);
  } catch (error) {
    console.error('Error updating demand:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE handler for removing a specific demand by ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // Find the index of the demand with the matching ID
    const demandIndex = mockDemands.findIndex((d) => d.id === id);

    // If no demand is found, return a 404 response
    if (demandIndex === -1) {
      return NextResponse.json({ error: 'Demand not found' }, { status: 404 });
    }

    // In a real app, you would delete from the database
    // For mock data, we'll just return a success message

    // Return success response
    return NextResponse.json({ success: true, message: 'Demand deleted successfully' });
  } catch (error) {
    console.error('Error deleting demand:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
