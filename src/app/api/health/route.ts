import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Add any health checks here (database, external services, etc.)
    // For now, just return a simple OK response
    
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'k2-customer-app',
      version: process.env.APP_VERSION || 'v1',
      environment: process.env.NODE_ENV || 'development'
    }, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'k2-customer-app',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { 
      status: 503 
    });
  }
}