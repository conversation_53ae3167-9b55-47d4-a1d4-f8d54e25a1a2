"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { checkUserExists, getHydraRedirectUrl, generateOtp, verifyOtp, getAccessToken, setCookie, getCookie, EMAIL_COOKIE, FLOW_ID_COOKIE, FLOW_TYPE_COOKIE } from "@/lib/auth";

let isTokenExchangeInProgress = false;

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [otp, setOtp] = useState("");
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [flowId, setFlowId] = useState("");
  const [flowType, setFlowType] = useState<"login" | "registration">("login");

  const { isAuthenticated, isLoading: authLoading, login } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  const processedFlowIds = useRef<Set<string>>(new Set());
  const processedAuthCodes = useRef<Set<string>>(new Set());

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      router.push("/assistant");
    }
  }, [isAuthenticated, authLoading, router]);

  useEffect(() => {
    let isCancelled = false;

    const checkUrlParams = async () => {
      try {
        // Check for authorization code
        const code = searchParams.get("code");
        if (code) {
          if (processedAuthCodes.current.has(code) || isTokenExchangeInProgress) {
            return;
          }

          processedAuthCodes.current.add(code);
          isTokenExchangeInProgress = true;

          if (!isCancelled) {
            setIsLoading(true);
            setError("");
          }

          try {
            const tokenData = await getAccessToken(code);

            if (!tokenData.access_token) {
              throw new Error("No access token received");
            }

            await login(tokenData.access_token);

            const url = new URL(window.location.href);
            url.searchParams.delete("code");
            window.history.replaceState({}, "", url.toString());

            router.push("/assistant");
          } catch (error) {
            console.error("Error exchanging code for token:", error);

            processedAuthCodes.current.delete(code);

            if (!isCancelled) {
              const errorMessage = error instanceof Error ? error.message : "Failed to complete authentication";
              setError(errorMessage);
            }
          } finally {
            // Always reset the global flag
            isTokenExchangeInProgress = false;

            if (!isCancelled) {
              setIsLoading(false);
            }
          }
          return;
        }

        // Check for flow ID
        const flowIdParam = searchParams.get("flow") || searchParams.get("flowId");

        // If we have a flow ID from URL, use it
        if (flowIdParam) {
          const sessionStorageKey = `processed_flow_${flowIdParam}`;
          const alreadyProcessedInRef = processedFlowIds.current.has(flowIdParam);
          const alreadyProcessedInStorage = sessionStorage.getItem(sessionStorageKey);

          if (alreadyProcessedInRef || alreadyProcessedInStorage) {
            return;
          }

          const storedEmail = getCookie(EMAIL_COOKIE) || "";
          const finalFlowType = (getCookie(FLOW_TYPE_COOKIE) || "login") as "login" | "registration";

          if (!storedEmail) {
            console.error("No email found in cookies");
            setError("Missing email information. Please try again.");
            return;
          }
          processedFlowIds.current.add(flowIdParam);
          sessionStorage.setItem(sessionStorageKey, "true");

          setIsLoading(true);
          setError("");
          setFlowId(flowIdParam);
          setEmail(storedEmail);
          setFlowType(finalFlowType);

          // Store values in cookies
          setCookie(FLOW_ID_COOKIE, flowIdParam);
          setCookie(FLOW_TYPE_COOKIE, finalFlowType);

          try {
            console.log("Generating OTP for flow ID:", flowIdParam);
            // Generate OTP for the flow
            await generateOtp({
              email: storedEmail,
              flowId: flowIdParam,
              flowType: finalFlowType,
              tenant: "default",
              clientName: "operator-app-client",
            });

            // OTP sent successfully
            setIsOtpSent(true);
            console.log("OTP generation successful for flow ID:", flowIdParam);
          } catch (otpError) {
            console.error("OTP generation failed:", otpError);
            processedFlowIds.current.delete(flowIdParam);
            sessionStorage.removeItem(sessionStorageKey);
            console.log("🧹 Cleaned up flow ID after failure:", flowIdParam);
            throw otpError;
          } finally {
            setIsLoading(false);
          }
        }
      } catch (err) {
        console.error("URL parameter processing error:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to process authentication parameters";
        setError(errorMessage);
        setIsOtpSent(false);
        setIsLoading(false);
      }
    };

    checkUrlParams();

    return () => {
      isCancelled = true;
      isTokenExchangeInProgress = false;
      // eslint-disable-next-line react-hooks/exhaustive-deps
      processedFlowIds.current.clear();
      // eslint-disable-next-line react-hooks/exhaustive-deps
      processedAuthCodes.current.clear();
    };
  }, [searchParams, login, router]);

  const handleSendOtp = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate email
    if (!email || !email.includes("@")) {
      setError("Please enter a valid email address");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Check if user exists to determine flow type
      const userFlowType = await checkUserExists(email);
      setFlowType(userFlowType);
      setCookie(FLOW_TYPE_COOKIE, userFlowType);

      // Store email in cookie
      setCookie(EMAIL_COOKIE, email);

      // Get redirect URL from Hydra
      const redirectData = await getHydraRedirectUrl(userFlowType);
      console.log("Hydra redirect data:", redirectData);

      // If we get a redirectTo URL, redirect to it
      // The auth service will redirect back to our app with the flowId
      if (redirectData.redirectTo) {
        window.location.href = redirectData.redirectTo;
        return;
      } else {
        throw new Error("Failed to initialize authentication flow");
      }
    } catch (err) {
      console.error("Login error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to send OTP. Please try again.";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!otp) {
      setError("Please enter the OTP");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Validate OTP format
      if (otp.length !== 6 || !/^\d+$/.test(otp)) {
        throw new Error("Please enter a valid 6-digit OTP");
      }

      // Verify OTP with the backend
      const verifyResponse = await verifyOtp({
        code: otp,
        flowId: flowId || undefined,
        flowType: flowType,
        email: email,
        tenant: "default",
        clientName: "operator-app-client",
      });

      console.log("Verify OTP response:", verifyResponse);

      // If we get a redirecting response, the verification is in progress
      if (verifyResponse.redirecting) {
        // The redirect is already happening, just return
        return;
      }

      // If we get a token directly, use it
      if (verifyResponse.token) {
        // Call the login function from auth context
        await login(verifyResponse.token);

        // Redirect to assistant
        router.push("/assistant");
        return;
      }

      // If we get here without a redirect or token, assume success but show a message
      setError("Authentication successful, but no token received. Please try again.");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to verify OTP";
      setError(errorMessage);

      // Reset OTP field but keep other data
      setOtp("");
    } finally {
      setIsLoading(false);
    }
  };

  if (authLoading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#7E5DED" />
                  <stop offset="100%" stopColor="#FF6B35" />
                </linearGradient>
              </defs>
              <path d="M30 5L50 15V45L30 55L10 45V15L30 5Z" fill="url(#logoGradient)" />
              <path d="M30 15L40 20V40L30 45L20 40V20L30 15Z" fill="white" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-gray-900">Klub Operator</h2>
          <p className="mt-2 text-sm text-gray-600">Sign in to access the operator dashboard</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Sign In</CardTitle>
            <CardDescription>{!isOtpSent ? "Enter your email to receive a verification code" : "Enter the verification code sent to your email"}</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={isOtpSent ? handleVerifyOtp : handleSendOtp} className="space-y-4">
              {!isOtpSent ? (
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} placeholder="Enter your email" required disabled={isLoading} />
                </div>
              ) : (
                <div className="space-y-2">
                  <Label htmlFor="otp">Verification Code</Label>
                  <Input
                    id="otp"
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    value={otp}
                    onChange={(e) => setOtp(e.target.value.replace(/[^0-9]/g, ""))}
                    placeholder="Enter 6-digit code"
                    maxLength={6}
                    required
                    disabled={isLoading}
                  />
                  <p className="text-sm text-gray-500">Code sent to {email}</p>
                </div>
              )}

              {error && <div className="text-red-600 text-sm">{error}</div>}

              <Button type="submit" className="w-full bg-[#7E5DED] hover:bg-[#6B4BC7]" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {!isOtpSent ? "Send OTP" : "Verify OTP"}
              </Button>

              {isOtpSent && (
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    setIsOtpSent(false);
                    setOtp("");
                    setError("");
                    processedFlowIds.current.clear();
                    processedAuthCodes.current.clear();
                  }}
                  disabled={isLoading}
                >
                  Back to Email
                </Button>
              )}
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
