export interface Escalation {
  id: string;
  session_id: string;
  requested_id: string;
  requestor_user_type: "USER" | "AGENT";
  assigned_operator_id: string;
  operator_user_type: "AGENT" | "USER";
  status: "pending" | "assigned" | "resolved" | "cancelled";
  assigned_at: string;
  resolved_at: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  meta: string; // JSON string
  is_archived: boolean;
}

export interface EscalationMeta {
  priority?: "low" | "medium" | "high" | "urgent";
  reason?: string;
  category?: string;
  notes?: string;
  [key: string]: unknown;
}

export interface EscalationListResponse {
  escalations: Escalation[];
  total_count: number;
}

export interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
  status: "success" | "error";
}

export interface EscalationFilters {
  status?: string;
  priority?: string;
  assigned_operator_id?: string;
  requestor_user_type?: string;
  page?: number;
  pageSize?: number;
}

export interface UpdateEscalationRequest {
  status?: "pending" | "assigned" | "resolved" | "cancelled";
  assigned_operator_id?: string;
  meta?: string;
}

export interface CreateEscalationRequest {
  session_id: string;
  requested_id: string;
  requestor_user_type: "USER" | "AGENT";
  meta?: string;
}
