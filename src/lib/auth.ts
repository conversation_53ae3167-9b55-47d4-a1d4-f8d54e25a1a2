/* eslint-disable @typescript-eslint/no-explicit-any */
import { jwtDecode } from "jwt-decode";

// Constants
export const AUTH_API_BASE_URL = process.env.NEXT_PUBLIC_AUTH_API_BASE_URL;
export const ORY_CLIENT_ID = process.env.NEXT_PUBLIC_OAUTH_CLIENT_ID;
export const REDIRECT_URI = process.env.NEXT_PUBLIC_OAUTH_REDIRECT_URL;

export const ACCESS_TOKEN_COOKIE = "access_token";
export const REFRESH_TOKEN_COOKIE = "refresh_token";
export const ID_TOKEN_COOKIE = "id_token";
export const FLOW_ID_COOKIE = "flow_id";
export const FLOW_TYPE_COOKIE = "flow_type";
export const EMAIL_COOKIE = "email";
export const NAME_COOKIE = "name";
export const PHONE_NUMBER_COOKIE = "phone_number";

// Helper functions
export const setCookie = (name: string, value: string, days = 7) => {
  if (typeof window === "undefined") return;

  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
};

export const getCookie = (name: string): string | null => {
  if (typeof window === "undefined") return null;

  const nameEQ = `${name}=`;
  const ca = document.cookie.split(";");

  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === " ") c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }

  return null;
};

export const deleteCookie = (name: string) => {
  if (typeof window === "undefined") return;
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;SameSite=Lax`;
};

export const getParameterByName = (name: string, url = window.location.href) => {
  name = name.replace(/[\[\]]/g, "\\$&");
  const regex = new RegExp(`[?&#]${name}(=([^&#]*)|&|#|$)`);
  const results = regex.exec(url);
  if (!results) return null;
  if (!results[2]) return "";
  return decodeURIComponent(results[2].replace(/\+/g, " "));
};

// Auth API functions
export const checkUserExists = async (email: string): Promise<"login" | "registration"> => {
  try {
    const encodedEmail = encodeURIComponent(email);
    const url = `${AUTH_API_BASE_URL}/user-exists?identifier=${encodedEmail}`;

    const res = await fetch(url);
    if (!res.ok) {
      throw new Error(`Failed to check user existence: ${res.status}`);
    }

    const { exists } = await res.json();
    return exists ? "login" : "registration";
  } catch (error) {
    console.error("Error checking user existence:", error);
    throw error;
  }
};

export const getHydraRedirectUrl = async (flowType: string) => {
  try {
    const state = Date.now();
    const scope = encodeURIComponent('offline openid offline_access api');
    const url = `${AUTH_API_BASE_URL}/initialize?flowType=${flowType}&client_id=${ORY_CLIENT_ID}&redirect_uri=${REDIRECT_URI}&response_type=code&state=${state}&scope=${scope}`;

    const result = await fetch(url);
    if (!result.ok) {
      throw new Error(`Failed to get redirect URL: ${result.status}`);
    }

    const data = await result.json();
    return data;
  } catch (error) {
    console.error("Error getting Hydra redirect URL:", error);
    throw error;
  }
};

interface GenerateOtpProps {
  email: string;
  flowId: string;
  flowType: string;
  name?: string;
  phoneNumber?: string;
  tenant?: string;
  clientName?: string;
}

export const generateOtp = async (data: GenerateOtpProps) => {
  try {
    const res = await fetch(`${AUTH_API_BASE_URL}/generate-otp`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!res.ok) {
      throw new Error(`Failed to generate OTP: ${res.status}`);
    }

    const result = await res.json();

    console.log("Generate OTP response:", result);

    if (result.redirect) {
      window.location.href = result.redirect;
    }

    return result;
  } catch (error) {
    console.error("Error generating OTP:", error);
    throw error;
  }
};

interface OtpVerifierProps {
  code: string;
  flowId?: string;
  flowType?: string;
  email?: string;
  name?: string;
  phoneNumber?: string;
  tenant?: string;
  clientName?: string;
}

export const verifyOtp = async (data: OtpVerifierProps) => {
  try {
    const response = await fetch(`${AUTH_API_BASE_URL}/verify-otp`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        code: data.code,
        flowId: data.flowId || getCookie(FLOW_ID_COOKIE),
        flowType: data.flowType || getCookie(FLOW_TYPE_COOKIE),
        email: data.email || getCookie(EMAIL_COOKIE),
        name: data.name || getCookie(NAME_COOKIE),
        phoneNumber: data.phoneNumber || getCookie(PHONE_NUMBER_COOKIE),
        clientName: data.clientName || "operator-app-client",
      }),
    });

    const resData = await response.json();

    if (resData?.redirect) {
      console.log("redirection url", resData.redirect);
      // If we get a redirect URL, redirect to it
      // The auth service will redirect back to our app with the code
      window.location.href = resData.redirect;
      return { redirecting: true };
    } else if (!response.ok) {
      console.error("OTP verification failed:", resData);
      throw new Error(resData.message || "OTP verification failed");
    }

    return resData;
  } catch (error) {
    console.error("Error verifying OTP:", error);

    throw error;
  }
};

export const getAccessToken = async (code: string) => {
  try {
    const res = await fetch(`${AUTH_API_BASE_URL}/token`, {
      method: 'POST',
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify({
        grant_type: "authorization_code",
        code,
        scope: "offline openid offline_access api",
        redirect_uri: REDIRECT_URI,
        client_id: ORY_CLIENT_ID,
      }),
    });

    if (!res.ok) {
      const error = await res.json();
      console.error("Token exchange failed:", error);
      throw new Error(error?.error || "Token fetch failed");
    }

    const tokenData = await res.json();

    // Store tokens securely
    setCookie(ACCESS_TOKEN_COOKIE, tokenData.access_token, 1); // Short lifetime for access token
    setCookie(REFRESH_TOKEN_COOKIE, tokenData.refresh_token, 30); // Longer lifetime for refresh token
    if (tokenData.id_token) {
      setCookie(ID_TOKEN_COOKIE, tokenData.id_token, 1);
    }

    return tokenData;
  } catch (error) {
    console.error("Error fetching access token:", error);
    throw error;
  }
};

// Keep track of refresh attempts to prevent infinite loops
let refreshAttempts = 0;
const MAX_REFRESH_ATTEMPTS = 3;

let refreshPromise: Promise<any> | null = null;

// Event for token refresh
export const TOKEN_REFRESH_EVENT = "token-refreshed";

export const refreshAccessToken = async () => {
  if (refreshPromise) {
    return refreshPromise;
  }

  // Check if we've exceeded the maximum number of refresh attempts
  if (refreshAttempts >= MAX_REFRESH_ATTEMPTS) {
    console.error("Maximum refresh attempts exceeded");
    await logout();
    if (typeof window !== "undefined") {
      window.location.href = "/login";
    }
    throw new Error("Maximum refresh attempts exceeded");
  }

  refreshPromise = performTokenRefresh();

  try {
    const result = await refreshPromise;
    return result;
  } finally {
    refreshPromise = null;
  }
};

const performTokenRefresh = async () => {
  refreshAttempts++;

  try {
    const refreshToken = getCookie(REFRESH_TOKEN_COOKIE);

    if (!refreshToken) {
      await logout();
      if (typeof window !== "undefined") {
        window.location.href = "/login";
      }
      throw new Error("No refresh token available");
    }

    const res = await fetch(`${AUTH_API_BASE_URL}/token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        grant_type: "refresh_token",
        refresh_token: refreshToken,
        scope: "offline openid offline_access api",
        client_id: ORY_CLIENT_ID,
      }),
    });

    if (!res.ok) {
      const errorData = await res.json();
      console.error("Token refresh failed:", errorData);
      // Clear auth state and redirect immediately
      await logout();
      if (typeof window !== "undefined") {
        window.location.href = "/login";
      }
      throw new Error(errorData?.error || "Token refresh failed");
    }

    const tokenData = await res.json();

    // Update stored tokens
    setCookie(ACCESS_TOKEN_COOKIE, tokenData.access_token, 1);
    if (tokenData.refresh_token) {
      setCookie(REFRESH_TOKEN_COOKIE, tokenData.refresh_token, 30);
    }
    if (tokenData.id_token) {
      setCookie(ID_TOKEN_COOKIE, tokenData.id_token, 1);
    }

    // Store the token in localStorage for compatibility with existing code
    if (typeof window !== "undefined") {
      localStorage.setItem("auth_token", tokenData.access_token);
    }

    // Reset refresh attempts on successful refresh
    refreshAttempts = 0;

    // Dispatch an event to notify the app that tokens have been refreshed
    if (typeof window !== "undefined") {
      const event = new CustomEvent(TOKEN_REFRESH_EVENT, { detail: tokenData });
      window.dispatchEvent(event);
    }

    return tokenData;
  } catch (error) {
    console.error("Error refreshing token:", error);
    // On any error, logout user
    await logout();
    if (typeof window !== "undefined") {
      window.location.href = "/login";
    }
    throw error;
  }
};

// Function to clear all cookies
export const clearAllCookies = () => {
  if (typeof document !== "undefined") {
    const cookies = document.cookie.split(";");

    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i];
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();

      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${window.location.hostname}`;

      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;

      if (window.location.hostname.indexOf(".") !== -1) {
        const domain = window.location.hostname.substring(window.location.hostname.indexOf("."));
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${domain}`;
      }
    }

    console.log("All cookies cleared");
  }
};

export const logoutFromHydra = async (): Promise<boolean> => {
  try {
    const hydraPublicUrl = process.env.NEXT_PUBLIC_HYDRA_BASE_URL;

    const idToken = getCookie(ID_TOKEN_COOKIE);

    if (!idToken) {
      console.log("No ID token found, skipping Hydra logout");
      return false;
    }

    const iframe = document.createElement("iframe");
    iframe.style.display = "none";
    document.body.appendChild(iframe);

    const timeoutId = setTimeout(() => {
      if (iframe && iframe.parentNode) {
        iframe.parentNode.removeChild(iframe);
      }
    }, 5000);

    const iframeLoadPromise = new Promise<boolean>((resolve) => {
      iframe.onload = () => {
        clearTimeout(timeoutId);

        setTimeout(() => {
          if (iframe && iframe.parentNode) {
            iframe.parentNode.removeChild(iframe);
          }
          resolve(true);
        }, 1000);
      };

      const logoutUrl = `${hydraPublicUrl}/hydra/oauth2/sessions/logout?id_token_hint=${idToken}`;
      console.log('Calling Hydra logout endpoint via iframe:', logoutUrl);
      iframe.src = logoutUrl;
    });

    const timeoutPromise = new Promise<boolean>((resolve) => {
      setTimeout(() => {
        console.log("Iframe load timed out, continuing with logout");
        resolve(false);
      }, 3000);
    });

    await Promise.race([iframeLoadPromise, timeoutPromise]);

    return true;
  } catch (error) {
    console.error("Error during Hydra logout:", error);
    return true;
  }
};

export const logout = async () => {
  // First try to logout from Hydra
  try {
    await logoutFromHydra();
  } catch (error) {
    console.error("Error during Hydra logout:", error);
  }

  // Clear all cookies
  clearAllCookies();

  deleteCookie(ACCESS_TOKEN_COOKIE);
  deleteCookie(REFRESH_TOKEN_COOKIE);
  deleteCookie(ID_TOKEN_COOKIE);
  deleteCookie(FLOW_ID_COOKIE);
  deleteCookie(FLOW_TYPE_COOKIE);
  deleteCookie(EMAIL_COOKIE);
  deleteCookie(NAME_COOKIE);
  deleteCookie(PHONE_NUMBER_COOKIE);

  // Clear localStorage
  if (typeof window !== "undefined") {
    localStorage.removeItem("auth_token");

    localStorage.removeItem("brandInfo");
    localStorage.removeItem("brandId");
    localStorage.removeItem("brandName");

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes("auth") || key.includes("token") || key.includes("user") || key.includes("brand"))) {
        localStorage.removeItem(key);
      }
    }
  }

  console.log("Logout completed, all cookies and storage cleared");
};

interface DecodedToken {
  exp: number;
  [key: string]: unknown;
}

export const isAuthenticated = (): boolean => {
  const accessToken = getCookie(ACCESS_TOKEN_COOKIE);
  const refreshToken = getCookie(REFRESH_TOKEN_COOKIE);

  // If no tokens at all, user is not authenticated
  if (!accessToken && !refreshToken) {
    return false;
  }

  try {
    if (accessToken) {
      const decodedToken = jwtDecode<DecodedToken>(accessToken);
      const currentTime = Date.now() / 1000;

      // If token is still valid (not expired), user is authenticated
      if (decodedToken.exp > currentTime) {
        return true;
      }

      // If access token is expired but we have refresh token, try to refresh
      if (refreshToken && !refreshPromise) {
        refreshAccessToken().catch((error) => {
          console.error("Token refresh failed:", error);
          // Refresh failed, logout user
          logout();
          if (typeof window !== "undefined") {
            window.location.href = "/login";
          }
        });
      }

      // Return false if access token is expired (refresh will happen in background)
      return false;
    }

    // If no access token but have refresh token, try to refresh
    if (refreshToken && !refreshPromise) {
      refreshAccessToken().catch((error) => {
        console.error("Token refresh failed:", error);
        // Refresh failed, logout user
        logout();
        if (typeof window !== "undefined") {
          window.location.href = "/login";
        }
      });
    }

    return false;
  } catch (error) {
    console.error("Error checking authentication:", error);
    // If there's an error decoding tokens, logout user
    logout();
    return false;
  }
};

export const getUserInfo = () => {
  const idToken = getCookie(ID_TOKEN_COOKIE);

  if (!idToken) return null;

  try {
    return jwtDecode<DecodedToken>(idToken);
  } catch (error) {
    console.error("Error decoding ID token:", error);
    return null;
  }
};
