import { get<PERSON><PERSON><PERSON>, ACCESS_TOKEN_COOKIE, refreshAccessToken } from "./auth";

const DEFAULT_API_BASE_URL = process.env.NEXT_PUBLIC_AUTH_API_BASE_URL || "http://localhost:3002";
const SERVICES_BASE_URL = process.env.NEXT_PUBLIC_SERVICES_BASE_URL || "http://localhost:8080";

const SERVICE_PATHS = {
  escalation: "/api/conversation-svc",
  conversation: "/api/conversation-svc",
  auth: "",
};

const SERVICE_URLS = {
  escalation: `${SERVICES_BASE_URL}${SERVICE_PATHS.escalation}`,
  conversation: `${SERVICES_BASE_URL}${SERVICE_PATHS.conversation}`,
  auth: process.env.NEXT_PUBLIC_AUTH_SERVICE_URL || DEFAULT_API_BASE_URL,
};

interface ApiOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  body?: unknown;
  headers?: Record<string, string>;
  requiresAuth?: boolean;
  service?: keyof typeof SERVICE_URLS;
}

/**
 * Generic API client for making HTTP requests
 */
export async function apiClient<T>(endpoint: string, options: ApiOptions = {}): Promise<T> {
  const { method = "GET", body, headers = {}, requiresAuth = true, service } = options;

  // Build request URL
  const baseUrl = service ? SERVICE_URLS[service] : DEFAULT_API_BASE_URL;
  const url = endpoint.startsWith("http") ? endpoint : `${baseUrl}${endpoint}`;

  // Set up headers
  const requestHeaders: Record<string, string> = {
    "Content-Type": "application/json",
    ...headers,
  };

  // Add authorization header if required
  if (requiresAuth) {
    const token = getCookie(ACCESS_TOKEN_COOKIE);
    if (!token) {
      throw new Error("Authentication required but no token found");
    }
    requestHeaders["Authorization"] = `Bearer ${token}`;
  }

  // Build request options
  const requestOptions: RequestInit = {
    method,
    headers: requestHeaders,
    credentials: "include",
  };

  // Add body for non-GET requests
  if (method !== "GET" && body) {
    requestOptions.body = JSON.stringify(body);
  }

  try {
    // Make the request
    const response = await fetch(url, requestOptions);

    // Handle 401 Unauthorized - attempt to refresh token and retry
    if (response.status === 401) {
      try {
        // Try to refresh the token
        await refreshAccessToken();

        // Update the authorization header with the new token
        const newToken = getCookie(ACCESS_TOKEN_COOKIE);
        if (newToken) {
          requestHeaders["Authorization"] = `Bearer ${newToken}`;

          // Retry the request with the new token
          const retryResponse = await fetch(url, {
            ...requestOptions,
            headers: requestHeaders,
          });

          if (!retryResponse.ok) {
            throw new Error(`API error: ${retryResponse.status}`);
          }

          return await retryResponse.json();
        } else {
          throw new Error("Failed to refresh authentication token");
        }
      } catch (refreshError) {
        console.error("Token refresh failed:", refreshError);
        throw new Error("Authentication failed");
      }
    }

    // Handle other error responses
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `API error: ${response.status} ${response.statusText}`);
    }

    // Parse and return the response
    return await response.json();
  } catch (error) {
    console.error("API request failed:", error);
    throw error;
  }
}

// Convenience object for making API calls
export const api = {
  get: <T>(endpoint: string, options?: Omit<ApiOptions, "method">) => apiClient<T>(endpoint, { ...options, method: "GET" }),

  post: <T>(endpoint: string, body?: unknown, options?: Omit<ApiOptions, "method" | "body">) => apiClient<T>(endpoint, { ...options, method: "POST", body }),

  put: <T>(endpoint: string, body?: unknown, options?: Omit<ApiOptions, "method" | "body">) => apiClient<T>(endpoint, { ...options, method: "PUT", body }),

  delete: <T>(endpoint: string, options?: Omit<ApiOptions, "method">) => apiClient<T>(endpoint, { ...options, method: "DELETE" }),

  patch: <T>(endpoint: string, body?: unknown, options?: Omit<ApiOptions, "method" | "body">) => apiClient<T>(endpoint, { ...options, method: "PATCH", body }),
};
