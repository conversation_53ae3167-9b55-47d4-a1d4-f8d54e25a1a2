"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useEscalations } from "@/hooks/use-escalations";
import { useAuth } from "@/hooks/use-auth";
import { escalationService } from "@/services/escalation-service";
import { Escalation, EscalationFilters } from "@/types/escalation";
import { RefreshCw, MessageSquare, Clock, User, AlertTriangle } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

// Add missing Badge component import
const Badge = ({ children, className }: { children: React.ReactNode; className?: string; variant?: string }) => (
  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${className}`}>{children}</span>
);

interface EscalationListProps {
  onEscalationSelect?: (escalation: Escalation) => void;
  showMyEscalationsOnly?: boolean;
}

export function EscalationList({ onEscalationSelect, showMyEscalationsOnly = false }: EscalationListProps) {
  const { user } = useAuth();
  const [filters, setFilters] = useState<EscalationFilters>({
    page: 1,
    pageSize: 20,
    ...(showMyEscalationsOnly && user?.id ? { assigned_operator_id: user.id } : {}),
  });

  const { escalations, totalCount, isLoading, error, assignEscalation, resolveEscalation, refreshEscalations } = useEscalations({ filters, autoRefresh: true, refreshInterval: 30000 });

  const handleFilterChange = (key: keyof EscalationFilters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value || undefined,
      page: 1, // Reset to first page when filtering
    }));
  };

  const handleAssignToMe = async (escalationId: string) => {
    if (!user?.id) return;

    try {
      await assignEscalation(escalationId);
    } catch (error) {
      console.error("Failed to assign escalation:", error);
    }
  };

  const handleResolve = async (escalationId: string) => {
    try {
      await resolveEscalation(escalationId);
    } catch (error) {
      console.error("Failed to resolve escalation:", error);
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "urgent":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case "high":
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Error loading escalations: {error}</p>
            <Button onClick={refreshEscalations} className="mt-2">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{showMyEscalationsOnly ? "My Escalations" : "All Escalations"}</h2>
          <p className="text-gray-600">
            {totalCount} escalation{totalCount !== 1 ? "s" : ""} found
          </p>
        </div>
        <Button onClick={refreshEscalations} disabled={isLoading} size="sm" variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-1 block">Status</label>
              <Select value={filters.status || ""} onValueChange={(value) => handleFilterChange("status", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="assigned">Assigned</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Priority</label>
              <Select value={filters.priority || ""} onValueChange={(value) => handleFilterChange("priority", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All priorities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All priorities</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">User Type</label>
              <Select value={filters.requestor_user_type || ""} onValueChange={(value) => handleFilterChange("requestor_user_type", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All types</SelectItem>
                  <SelectItem value="USER">Customer</SelectItem>
                  <SelectItem value="AGENT">Agent</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Page Size</label>
              <Select value={filters.pageSize?.toString() || "20"} onValueChange={(value) => handleFilterChange("pageSize", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Escalation List */}
      <div className="space-y-3">
        {isLoading && escalations.length === 0 ? (
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
                <p className="text-gray-600">Loading escalations...</p>
              </div>
            </CardContent>
          </Card>
        ) : escalations.length === 0 ? (
          <Card>
            <CardContent className="p-6">
              <div className="text-center text-gray-600">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p>No escalations found</p>
                <p className="text-sm">Try adjusting your filters or check back later.</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          escalations.map((escalation) => {
            const priority = escalationService.getPriority(escalation);
            const priorityColor = escalationService.getPriorityColor(priority);
            const statusColor = escalationService.getStatusColor(escalation.status);
            const isAssignedToMe = escalation.assigned_operator_id === user?.id;

            return (
              <Card key={escalation.id} className={`cursor-pointer hover:shadow-md transition-shadow ${isAssignedToMe ? "ring-2 ring-blue-200" : ""}`} onClick={() => onEscalationSelect?.(escalation)}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center space-x-2">
                        {getPriorityIcon(priority)}
                        <Badge className={priorityColor}>{priority.toUpperCase()}</Badge>
                        <Badge className={statusColor}>{escalation.status.toUpperCase()}</Badge>
                        {isAssignedToMe && (
                          <Badge variant="outline" className="text-blue-600 border-blue-200">
                            Assigned to me
                          </Badge>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Session ID:</span>
                          <p className="text-gray-600 font-mono text-xs">{escalation.session_id}</p>
                        </div>
                        <div>
                          <span className="font-medium">Requestor:</span>
                          <p className="text-gray-600">{escalation.requestor_user_type === "USER" ? "Customer" : "Agent"}</p>
                        </div>
                        <div>
                          <span className="font-medium">Created:</span>
                          <p className="text-gray-600">{formatDistanceToNow(new Date(escalation.created_at), { addSuffix: true })}</p>
                        </div>
                        <div>
                          <span className="font-medium">Updated:</span>
                          <p className="text-gray-600">{formatDistanceToNow(new Date(escalation.updated_at), { addSuffix: true })}</p>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col space-y-2 ml-4">
                      {escalation.status === "pending" && (
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAssignToMe(escalation.id);
                          }}
                        >
                          <User className="h-4 w-4 mr-1" />
                          Assign to Me
                        </Button>
                      )}

                      {escalation.status === "assigned" && isAssignedToMe && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleResolve(escalation.id);
                          }}
                        >
                          Mark Resolved
                        </Button>
                      )}

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          onEscalationSelect?.(escalation);
                        }}
                      >
                        <MessageSquare className="h-4 w-4 mr-1" />
                        View Chat
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
}
