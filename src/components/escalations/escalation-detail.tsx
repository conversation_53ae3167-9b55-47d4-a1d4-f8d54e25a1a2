"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useEscalationById } from "@/hooks/use-escalations";
import { useChatHistory } from "@/hooks/use-conversations";
import { useWebSocket } from "@/hooks/use-websocket";
import { useAuth } from "@/hooks/use-auth";
import { escalationService } from "@/services/escalation-service";
interface SimpleMessageProps {
  message: {
    content: string;
    timestamp?: Date;
  };
  isUser: boolean;
}

const SimpleMessage = ({ message, isUser }: SimpleMessageProps) => (
  <div className="flex w-full items-start gap-4">
    <div className="w-8 h-8 flex-shrink-0 rounded-full bg-gray-300 flex items-center justify-center">{isUser ? "U" : "A"}</div>
    <div className="flex-1">
      <div className="bg-white border rounded-lg p-3 shadow-sm">
        <p className="text-sm text-gray-900">{message.content}</p>
        <p className="text-xs text-gray-500 mt-1">{message.timestamp ? new Date(message.timestamp).toLocaleTimeString() : ""}</p>
      </div>
    </div>
  </div>
);
import { ArrowLeft, Send, User, AlertTriangle, CheckCircle } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

// Badge component
const Badge = ({ children, className }: { children: React.ReactNode; className?: string; variant?: string }) => (
  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${className}`}>{children}</span>
);

interface EscalationDetailProps {
  escalationId: string;
  onBack?: () => void;
}

export function EscalationDetail({ escalationId, onBack }: EscalationDetailProps) {
  const { user } = useAuth();
  const { escalation, isLoading: escalationLoading, error: escalationError } = useEscalationById(escalationId);
  const { chatHistory, isLoading: chatLoading, addMessage } = useChatHistory(escalation?.session_id || "");
  const { sendMessage, isLoading: sendingMessage } = useWebSocket();

  const [message, setMessage] = useState("");
  const [isResolving, setIsResolving] = useState(false);

  // Handle new messages from WebSocket
  useEffect(() => {
    // This would be handled by the WebSocket hook automatically
    // when new messages arrive for this session
  }, []);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || !escalation || sendingMessage) return;

    try {
      // Send message via WebSocket
      await sendMessage(message);

      // Add message to local chat history immediately for better UX
      const newMessage = {
        id: `temp-${Date.now()}`,
        role: "user" as const,
        content: message,
        timestamp: new Date(),
        type: "text_req",
        sender_id: user?.id,
      };

      addMessage(newMessage);
      setMessage("");
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const handleResolveEscalation = async () => {
    if (!escalation || !user?.id) return;

    setIsResolving(true);
    try {
      await escalationService.resolveEscalation(escalation.id, user.id);
      // Optionally navigate back or show success message
    } catch (error) {
      console.error("Failed to resolve escalation:", error);
    } finally {
      setIsResolving(false);
    }
  };

  const handleAssignToMe = async () => {
    if (!escalation || !user?.id) return;

    try {
      await escalationService.assignEscalation(escalation.id, user.id);
    } catch (error) {
      console.error("Failed to assign escalation:", error);
    }
  };

  if (escalationLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p>Loading escalation details...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (escalationError || !escalation) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Error loading escalation: {escalationError || "Escalation not found"}</p>
            <Button onClick={onBack} className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const priority = escalationService.getPriority(escalation);
  const priorityColor = escalationService.getPriorityColor(priority);
  const statusColor = escalationService.getStatusColor(escalation.status);
  const isAssignedToMe = escalation.assigned_operator_id === user?.id;
  const canTakeAction = escalation.status !== "resolved" && escalation.status !== "cancelled";

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <Card className="mb-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm" onClick={onBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div>
                <CardTitle className="text-xl">Escalation Details</CardTitle>
                <p className="text-sm text-gray-600">Session: {escalation.session_id}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className={priorityColor}>
                <AlertTriangle className="h-3 w-3 mr-1" />
                {priority.toUpperCase()}
              </Badge>
              <Badge className={statusColor}>{escalation.status.toUpperCase()}</Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium">Requestor:</span>
              <p className="text-gray-600">{escalation.requestor_user_type === "USER" ? "Customer" : "Agent"}</p>
            </div>
            <div>
              <span className="font-medium">Created:</span>
              <p className="text-gray-600">{formatDistanceToNow(new Date(escalation.created_at), { addSuffix: true })}</p>
            </div>
            <div>
              <span className="font-medium">Updated:</span>
              <p className="text-gray-600">{formatDistanceToNow(new Date(escalation.updated_at), { addSuffix: true })}</p>
            </div>
            <div>
              <span className="font-medium">Assigned:</span>
              <p className="text-gray-600">{isAssignedToMe ? "You" : escalation.assigned_operator_id ? "Another operator" : "Unassigned"}</p>
            </div>
          </div>

          {/* Action Buttons */}
          {canTakeAction && (
            <div className="flex items-center space-x-2 mt-4">
              {escalation.status === "pending" && (
                <Button onClick={handleAssignToMe} size="sm">
                  <User className="h-4 w-4 mr-2" />
                  Assign to Me
                </Button>
              )}

              {escalation.status === "assigned" && isAssignedToMe && (
                <Button onClick={handleResolveEscalation} disabled={isResolving} size="sm" variant="outline">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {isResolving ? "Resolving..." : "Mark Resolved"}
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Chat Interface */}
      <div className="flex-1 flex flex-col">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 rounded-lg">
          {chatLoading ? (
            <div className="text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto mb-2"></div>
              <p className="text-sm text-gray-600">Loading chat history...</p>
            </div>
          ) : chatHistory.length === 0 ? (
            <div className="text-center text-gray-600">
              <p>No messages in this conversation yet.</p>
            </div>
          ) : (
            chatHistory.map((msg, index) => <SimpleMessage key={msg.id || index} message={msg} isUser={msg.role === "user"} />)
          )}
        </div>

        {/* Message Input */}
        {canTakeAction && isAssignedToMe && (
          <Card className="mt-4">
            <CardContent className="p-4">
              <form onSubmit={handleSendMessage} className="flex space-x-2">
                <Textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your message to the customer..."
                  className="flex-1 min-h-[60px] resize-none"
                  disabled={sendingMessage}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage(e);
                    }
                  }}
                />
                <Button type="submit" disabled={!message.trim() || sendingMessage} className="self-end">
                  <Send className="h-4 w-4" />
                </Button>
              </form>
              <p className="text-xs text-gray-500 mt-2">Press Enter to send, Shift+Enter for new line</p>
            </CardContent>
          </Card>
        )}

        {!isAssignedToMe && escalation.status === "assigned" && (
          <Card className="mt-4">
            <CardContent className="p-4 text-center text-gray-600">
              <p>This escalation is assigned to another operator.</p>
              <p className="text-sm">You cannot send messages in this conversation.</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
