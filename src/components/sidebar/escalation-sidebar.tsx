"use client";

import { useSidebarEscalations } from "@/hooks/use-sidebar-escalations";
import { Escalation } from "@/types/escalation";
import { Clock, User } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface EscalationSidebarProps {
  onTaskClick?: (sessionId: string) => void;
}

export function EscalationSidebar({ onTaskClick }: EscalationSidebarProps) {
  const {
    assignedTasks,
    pendingTasks,
    assignedCount,
    pendingCount,
    isLoading,
    isLoadingMoreAssigned,
    isLoadingMorePending,
    hasMoreAssigned,
    hasMorePending,
    error,
    handleAssignedScroll,
    handlePendingScroll,
    assignTask,
    resolveTask,
  } = useSidebarEscalations();

  const AssignedTaskItem = ({ escalation }: { escalation: Escalation }) => {
    let userName = "";
    let description = "";

    if (typeof escalation.meta === "string") {
      try {
        escalation.meta = JSON.parse(escalation.meta);
      } catch (error) {
        console.error("Error parsing escalation meta:", error);
      }
    }

    try {
      if (escalation.meta && typeof escalation.meta === "object") {
        const meta = escalation.meta as { userName?: string; description?: string };
        userName = meta.userName || "";
        description = meta.description || "";
      }
    } catch (error) {
      console.error("Error parsing escalation meta:", error);
    }

    return (
      <div className="p-2 border-b border-gray-100 last:border-b-0 cursor-pointer hover:bg-gray-50" onClick={() => onTaskClick?.(escalation.session_id)}>
        <div className="flex items-start gap-2 text-xs">
          <div className="flex-shrink-0">
            <Clock className="h-3 w-3 text-gray-400" />
          </div>

          <div className="flex-1 min-w-0">
            {userName && <div className="text-gray-700 font-medium mb-1 truncate">{userName}</div>}

            {description && <div className="text-gray-600 text-xs mb-1 truncate">{description}</div>}

            <div className="text-gray-600 truncate">Session: {escalation.session_id.slice(0, 8)}...</div>
            <div className="text-gray-500 text-xs mb-2">{formatDistanceToNow(new Date(escalation.created_at), { addSuffix: true })}</div>

            <button
              onClick={(e) => {
                e.stopPropagation();
                resolveTask(escalation.id);
              }}
              className="bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs font-medium"
            >
              Mark as Resolved
            </button>
          </div>
        </div>
      </div>
    );
  };

  const PendingTaskItem = ({ escalation }: { escalation: Escalation }) => {
    let userName = "";
    let description = "";

    if (typeof escalation.meta === "string") {
      try {
        escalation.meta = JSON.parse(escalation.meta);
      } catch (error) {
        console.error("Error parsing escalation meta:", error);
      }
    }

    try {
      if (escalation.meta && typeof escalation.meta === "object") {
        const meta = escalation.meta as { userName?: string; description?: string };
        userName = meta.userName || "";
        description = meta.description || "";
      }
    } catch (error) {
      console.error("Error parsing escalation meta:", error);
    }

    return (
      <div className="p-2 border-b border-gray-100">
        <div className="flex items-start gap-2 text-xs">
          <div className="flex-shrink-0">
            <Clock className="h-3 w-3 text-gray-400" />
          </div>

          <div className="flex-1 min-w-0">
            {userName && <div className="text-gray-700 font-medium mb-1 truncate">{userName}</div>}

            {description && <div className="text-gray-600 text-xs mb-1 truncate">{description}</div>}

            <div className="text-gray-600 truncate">Session: {escalation.session_id.slice(0, 8)}...</div>
            <div className="text-gray-500 text-xs mb-2">{formatDistanceToNow(new Date(escalation.created_at), { addSuffix: true })}</div>

            <button onClick={() => assignTask(escalation.id)} className="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
              Assign to Me
            </button>
          </div>
        </div>
      </div>
    );
  };

  const SectionHeader = ({ title, count, icon }: { title: string; count: number; icon: React.ReactNode }) => (
    <div className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-gray-700 border-b border-gray-200">
      {icon}
      <span className="flex-1">{title}</span>
      <span className="bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded-full text-xs">{count}</span>
    </div>
  );

  if (error) {
    return <div className="p-2 text-xs text-red-600">Error: {error}</div>;
  }

  return (
    <div className="flex flex-col h-full border-t border-gray-200">
      <div className="flex-[0.5] flex flex-col min-h-0">
        <SectionHeader title="Tasks Assigned" count={assignedCount} icon={<User className="h-3 w-3" />} />

        <div className="flex-1 overflow-y-auto min-h-0" onScroll={handleAssignedScroll}>
          {isLoading && assignedTasks.length === 0 ? (
            <div className="p-2 text-xs text-gray-500">Loading...</div>
          ) : assignedTasks.length === 0 ? (
            <div className="p-2 text-xs text-gray-500">No assigned tasks</div>
          ) : (
            <>
              {assignedTasks.map((escalation) => (
                <AssignedTaskItem key={escalation.id} escalation={escalation} />
              ))}
              {isLoadingMoreAssigned && (
                <div className="p-2 text-xs text-gray-500 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-400 mr-1"></div>
                  Loading more...
                </div>
              )}
              {hasMoreAssigned && !isLoadingMoreAssigned && <div className="p-2 text-xs text-gray-400 text-center">Scroll down for more</div>}
            </>
          )}
        </div>
      </div>

      <div className="flex-[0.5] flex flex-col min-h-0 border-t border-gray-100">
        <SectionHeader title="Tasks in Queue" count={pendingCount} icon={<Clock className="h-3 w-3" />} />

        <div className="flex-1 overflow-y-auto min-h-0" onScroll={handlePendingScroll}>
          {isLoading && pendingTasks.length === 0 ? (
            <div className="p-2 text-xs text-gray-500">Loading...</div>
          ) : pendingTasks.length === 0 ? (
            <div className="p-2 text-xs text-gray-500">No pending tasks</div>
          ) : (
            <>
              {pendingTasks.map((escalation) => (
                <PendingTaskItem key={escalation.id} escalation={escalation} />
              ))}
              {isLoadingMorePending && (
                <div className="p-2 text-xs text-gray-500 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-400 mr-1"></div>
                  Loading more...
                </div>
              )}
              {hasMorePending && !isLoadingMorePending && <div className="p-2 text-xs text-gray-400 text-center">Scroll down for more</div>}
            </>
          )}
        </div>
      </div>

      <div className="p-1 text-xs text-gray-400 border-t border-gray-100 flex-shrink-0">Auto-refresh: 30s</div>
    </div>
  );
}
