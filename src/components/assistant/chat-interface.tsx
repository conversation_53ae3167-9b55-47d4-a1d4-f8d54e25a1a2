"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ChatMessage } from "./chat-message";
import { ConversationMessage } from "./conversation-message";
import { useWebSocket } from "@/hooks/use-websocket";
import { useConversations } from "@/hooks/use-conversations";
import Image from "next/image";

interface ChatInterfaceProps {
  sessionId?: string;
  escalationSessionId?: string;
  onBackToChat?: () => void;
  onBackFromEscalation?: () => void;
}

export function ChatInterface({ sessionId, escalationSessionId, onBackToChat, onBackFromEscalation }: ChatInterfaceProps) {
  const [input, setInput] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Pass escalation session ID to useWebSocket when viewing an escalation conversation
  const { messages, sendMessage, isLoading, isConnected, isReady, clearChatHistory } = useWebSocket({
    escalationSessionId: escalationSessionId,
  });
  const { conversations, isLoading: conversationsLoading, isLoadingMore, hasMore, loadConversationsBySession, clearConversations, createLoadMoreHandler } = useConversations();

  const isEscalationMode = !!escalationSessionId;

  useEffect(() => {
    if (isEscalationMode && escalationSessionId) {
      console.log("🔄 Loading conversation history for escalation session:", escalationSessionId);
      clearChatHistory();
      loadConversationsBySession(escalationSessionId);
    } else if (sessionId) {
      loadConversationsBySession(sessionId);
    } else {
      clearConversations();
      clearChatHistory();
    }
  }, [sessionId, escalationSessionId, isEscalationMode, loadConversationsBySession, clearConversations, clearChatHistory]);

  const handleLoadMore = useCallback(() => {
    if (isEscalationMode && escalationSessionId) {
      const loadMoreHandler = createLoadMoreHandler(escalationSessionId);
      loadMoreHandler();
    }
  }, [isEscalationMode, escalationSessionId, createLoadMoreHandler]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, conversations]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading && isEscalationMode) {
      sendMessage(input);
      setInput("");
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div ref={messagesContainerRef} className="flex-grow overflow-y-auto px-4 py-2 flex justify-center">
        <div className="max-w-[870px] w-full space-y-4">
          {sessionId && onBackToChat && (
            <div className="mb-4">
              <button onClick={onBackToChat} className="flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm">
                ← Back to Chat
              </button>
            </div>
          )}

          {/* Show escalation header when in escalation mode */}
          {isEscalationMode && onBackFromEscalation && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-blue-800">Escalation Chat Active</h3>
                  <p className="text-xs text-blue-600">Session: {escalationSessionId}</p>
                </div>
                <button onClick={onBackFromEscalation} className="text-blue-600 hover:text-blue-800 text-sm px-3 py-1 border border-blue-300 rounded">
                  Exit Escalation
                </button>
              </div>
            </div>
          )}

          {/* Show conversation history and live messages for escalations */}
          {isEscalationMode ? (
            <>
              {conversationsLoading ? (
                <div className="flex items-center justify-center h-full min-h-[300px]">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
                    <p className="text-gray-500">Loading conversation history...</p>
                  </div>
                </div>
              ) : (
                <>
                  {/* Show loading indicator for pagination */}
                  {isLoadingMore && (
                    <div className="flex items-center justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-600 mr-2"></div>
                      <span className="text-sm text-gray-500">Loading more messages...</span>
                    </div>
                  )}

                  {hasMore && !isLoadingMore && (
                    <div className="flex justify-center py-4">
                      <button onClick={handleLoadMore} className="px-4 py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 border border-blue-200 rounded-md">
                        Load More Messages
                      </button>
                    </div>
                  )}

                  {conversations.length > 0 && (
                    <>
                      <div className="text-xs text-gray-500 mb-2">Conversation History ({conversations.length} messages)</div>
                      {conversations.map((conversation) => (
                        <ConversationMessage key={conversation.id} conversation={conversation} />
                      ))}
                    </>
                  )}

                  {messages.length > 0 && (
                    <>
                      <div className="text-xs text-gray-500 mb-2 mt-4 border-t pt-2">Live Messages ({messages.length} messages)</div>
                      {messages.map((message, index) => (
                        <ChatMessage key={`realtime-${index}`} message={message} isUser={message.role === "user"} timestamp={message.timestamp} />
                      ))}
                    </>
                  )}

                  {conversations.length === 0 && messages.length === 0 && (
                    <div className="flex items-center justify-center h-full min-h-[300px]">
                      <div className="text-center">
                        <div className="flex justify-center mb-4">
                          <Image src="/star-logo.svg" alt="Star Logo" width={32} height={32} />
                        </div>
                        <h3 className="text-lg font-medium">Klub AI Operator</h3>
                        <p className="text-gray-500 mt-2">Ready to Chat</p>
                        <p className="text-xs text-gray-400 mt-1">Session: {escalationSessionId}</p>
                      </div>
                    </div>
                  )}
                </>
              )}
            </>
          ) : sessionId ? (
            <>
              {conversationsLoading ? (
                <div className="flex items-center justify-center h-full min-h-[300px]">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
                    <p className="text-gray-500">Loading conversation...</p>
                  </div>
                </div>
              ) : conversations.length === 0 ? (
                <div className="flex items-center justify-center h-full min-h-[300px]">
                  <div className="text-center">
                    <p className="text-gray-500">No messages found in this conversation.</p>
                  </div>
                </div>
              ) : (
                conversations.map((conversation) => <ConversationMessage key={conversation.id} conversation={conversation} />)
              )}
            </>
          ) : (
            <>
              {messages.length === 0 ? (
                <div className="flex items-center justify-center h-full min-h-[300px]">
                  <div className="text-center">
                    <div className="flex justify-center mb-4">
                      <Image src="/star-logo.svg" alt="Star Logo" width={32} height={32} />
                    </div>
                    <h3 className="text-lg font-medium">Klub AI Operator</h3>
                    <p className="text-gray-500 mt-2">Select an escalation task from the sidebar to start chatting with customers.</p>
                  </div>
                </div>
              ) : (
                messages.map((message, index) => <ChatMessage key={index} message={message} isUser={message.role === "user"} timestamp={message.timestamp} />)
              )}
            </>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      <div className="flex-shrink-0 p-4 bg-white flex justify-center">
        <div className="max-w-[870px] w-full flex items-center pb-[15.5px] pt-[15.5px]">
          <form onSubmit={handleSubmit} className="relative w-full">
            <div className={`relative flex items-center bg-white bg-opacity-60 border rounded-md h-[53px] ${isEscalationMode ? "border-[#DFE1E4]" : "border-gray-300 bg-gray-50"}`}>
              <Textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder={isEscalationMode ? "Type your response to this escalation..." : "Select an escalation task to enable chat"}
                className={`resize-none flex-grow w-full border-none py-[15.5px] px-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 text-base font-normal ${
                  isEscalationMode ? "text-[rgba(29,29,27,0.8)]" : "text-gray-400"
                }`}
                rows={1}
                disabled={!isEscalationMode || !isConnected || !isReady}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(e);
                  }
                }}
              />
              <Button
                type="submit"
                size="icon"
                disabled={!isEscalationMode || isLoading || !input.trim() || !isConnected || !isReady}
                className={`flex items-center justify-center w-[34px] h-[34px] rounded-[30px] mr-2 ${isEscalationMode ? "bg-[#9B81F5] hover:bg-[#8A70E4]" : "bg-gray-300 cursor-not-allowed"}`}
              >
                <Image src="/send-button.svg" alt="Send" width={34} height={34} />
              </Button>
            </div>
          </form>
          {isLoading && isEscalationMode && <div className="text-xs text-gray-500 mt-3 absolute bottom-[-20px] pb-[15.5px]">Sending response...</div>}
        </div>
      </div>
    </div>
  );
}
