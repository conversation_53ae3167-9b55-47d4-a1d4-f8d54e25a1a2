"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ActionMessage } from "@/hooks/use-websocket";
import { FormField } from "./dynamic-form-modal";
import { DynamicFormModal } from "./dynamic-form-modal";

interface CardStyle {
  backgroundColor: string;
  buttonColor: string;
  title: string;
  description: string;
  buttonText: string;
}

const getCardStyle = (title: string, category: string): CardStyle => {
  const styleMap: Record<string, CardStyle> = {
    "Onboarding Journey": {
      backgroundColor: "bg-[#e0f9f1]",
      buttonColor: "bg-[#01734e]",
      title: "Onboarding Journey",
      description: "Create your brand profile to raise capital.",
      buttonText: "Complete Profile",
    },
    "Explore More": {
      backgroundColor: "bg-[#e0f9f1cc]",
      buttonColor: "bg-[#01734e]",
      title: "Explore More",
      description: "Login to explore the offers",
      buttonText: "Login",
    },
    "Funding Options": {
      backgroundColor: "bg-[#ffefd5]",
      buttonColor: "bg-[#ff7f50]",
      title: "Funding Options",
      description: "Discover various funding options for your business.",
      buttonText: "Explore Options",
    },
    "Financial Insights": {
      backgroundColor: "bg-[#e6f7ff]",
      buttonColor: "bg-[#1890ff]",
      title: "Financial Insights",
      description: "Get insights into your financial performance.",
      buttonText: "View Insights",
    },
  };

  if (title && styleMap[title]) {
    return styleMap[title];
  }

  switch (category) {
    case "login":
      return styleMap["Explore More"];
    case "form":
      return styleMap["Onboarding Journey"];
    case "funding":
      return styleMap["Funding Options"];
    case "insights":
      return styleMap["Financial Insights"];
    default:
      return {
        backgroundColor: "bg-[#f0f0f0]",
        buttonColor: "bg-[#333333]",
        title: title || "Action Required",
        description: "Please take action to proceed.",
        buttonText: "Continue",
      };
  }
};

interface ActionCardProps {
  action: ActionMessage;
  title?: string;
  description?: string;
  buttonText?: string;
  buttonColor?: string;
  backgroundColor?: string;
}

const ActionCardTemplate: React.FC<ActionCardProps> = ({ action, title, description, buttonText, buttonColor, backgroundColor }) => {
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);

  const handleButtonClick = () => {
    if (action.category === "form") {
      setIsFormModalOpen(true);
    }
    // Handle other action types as needed
  };

  return (
    <>
      <Card
        className={`${backgroundColor} rounded-[4px] border-0 shadow-none`}
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-start",
          padding: "30px 0px 30px 30px",
          gap: "10px",
          width: "420px",
          background: backgroundColor,
          borderRadius: "4px",
          flex: "none",
          order: 0,
          flexGrow: 0,
        }}
      >
        <CardContent className="p-0">
          <div className="flex flex-col items-start gap-[15px]">
            <h3 className="opacity-80 font-sans font-semibold text-[#1d1d1b] text-lg">{title}</h3>
            <p className="opacity-80 font-sans font-normal text-[#1d1d1bcc] text-base w-[364px]">{description}</p>
            <Button className={`h-[45px] ${buttonColor} rounded-[3px] font-normal text-white text-base`} onClick={handleButtonClick}>
              {buttonText}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Dynamic Form Modal */}
      {action.category === "form" && action.data && (
        <DynamicFormModal
          isOpen={isFormModalOpen}
          onClose={() => setIsFormModalOpen(false)}
          formData={action.data as FormField[]}
          title="Brand Profile Creation"
          description="Please complete your brand profile to proceed with your application."
        />
      )}
    </>
  );
};

export function ActionCard({ action }: ActionCardProps) {
  const actionTitle = (action.title as string) || "";

  const cardStyle = getCardStyle(actionTitle, action.category);

  return (
    <ActionCardTemplate
      action={action}
      title={cardStyle.title}
      description={cardStyle.description}
      buttonText={cardStyle.buttonText}
      buttonColor={cardStyle.buttonColor}
      backgroundColor={cardStyle.backgroundColor}
    />
  );
}
