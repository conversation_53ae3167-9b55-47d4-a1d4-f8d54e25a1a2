"use client";

import Image from "next/image";
import { ActionCard } from "./action-card";
import {
  ChatMessage as ChatMessageType,
  ActionMessage,
  ActionRequestMessage,
  BubblesMessage,
  SessionStartResponseMessage,
  SystemMessage,
  AgentSuggestionMessage,
  TextResponseMessage,
} from "@/hooks/use-websocket";
import { AlertCircle } from "lucide-react";

interface ChatMessageProps {
  message: ChatMessageType;
  isUser: boolean;
  timestamp?: Date;
}

export function ChatMessage({ message, isUser }: ChatMessageProps) {
  // Helper function to get message text from different message types
  const getMessageText = (msg: ChatMessageType): string => {
    switch (msg.type) {
      case "text_req":
        return msg.payload?.message || "";
      case "text_resp":
        return (msg as TextResponseMessage).payload?.message || "";
      case "action":
        return (msg as ActionMessage).message || "";
      case "action_req":
        return (msg as ActionRequestMessage).payload?.message || "";
      case "bubbles":
        return (msg as BubblesMessage).payload?.message || "";
      case "session_start_resp":
        return (msg as SessionStartResponseMessage).payload?.message || "";
      case "system":
        return (msg as SystemMessage).payload?.message || "";
      case "agent_suggestion":
        return (msg as AgentSuggestionMessage).payload?.suggestion || "";
      default:
        return "";
    }
  };

  const messageText = getMessageText(message);
  const isErrorMessage = messageText.toLowerCase().startsWith("error:");

  return (
    <div className="flex w-[870px] items-start gap-[20px]">
      <div className="w-[27px] h-[27px] flex-shrink-0">
        {!isUser && <Image src="/ai-logo.svg" alt="Klub AI" width={27} height={27} />}
        {isUser && (
          <Image src="/user-logo.svg" alt="Klub AI" width={27} height={27} />
          // <div className="w-full h-full rounded-full bg-[rgba(223,54,12,0.8)] flex items-center justify-center text-white">
          //   {getUserInitials()}
          // </div>
        )}
      </div>

      {/* Message Content */}
      <div className="w-full space-y-6">
        {message.type === "text_req" || message.type === "text_resp" || message.type === "session_start_resp" || message.type === "agent_suggestion" ? (
          isErrorMessage ? (
            <div className="rounded-lg px-4 py-3 bg-red-50 border border-red-200 text-red-800 flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Sorry, something went wrong</p>
                <p className="text-sm mt-1">We&apos;re experiencing technical difficulties. Please try again later or contact support if the problem persists.</p>
              </div>
            </div>
          ) : (
            <div
              className={`w-full dm-sans ${
                message.type === "agent_suggestion"
                  ? "bg-green-50 border border-green-200 rounded-lg px-4 py-3"
                  : message.type === "session_start_resp"
                    ? "bg-blue-50 border border-blue-200 rounded-lg px-4 py-3"
                    : ""
              }`}
              style={{
                minHeight: "46px",
                fontStyle: "normal",
                fontFamily: "DM Sans",
                fontWeight: 400,
                fontSize: "18px",
                lineHeight: "23px",
                color: message.type === "agent_suggestion" ? "#059669" : message.type === "session_start_resp" ? "#1E40AF" : "#1D1D1B",
              }}
            >
              {message.type === "agent_suggestion" && <span className="text-sm font-medium text-green-600 block mb-1">Agent Suggestion</span>}
              {message.type === "session_start_resp" && <span className="text-sm font-medium text-blue-600 block mb-1">Session Started</span>}
              {messageText}
            </div>
          )
        ) : (
          <div className="w-full space-y-6">
            {/* Display the message text if it exists in the action message */}
            {messageText && (
              <div
                className="w-full dm-sans"
                style={{
                  minHeight: "46px",
                  fontFamily: "DM Sans",
                  fontStyle: "normal",
                  fontWeight: 400,
                  fontSize: "18px",
                  lineHeight: "23px",
                  color: "#1D1D1B",
                }}
              >
                {messageText}
              </div>
            )}

            {/* Display the action card */}
            {message.type === "action" && (
              <div className="flex flex-wrap gap-[20px] mt-4 -ml-[30px]">
                <ActionCard action={message as ActionMessage} />
              </div>
            )}

            {/* Display action request */}
            {message.type === "action_req" && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg px-4 py-3">
                <span className="text-sm font-medium text-yellow-600 block mb-1">Action Required</span>
                <div className="text-yellow-800">{(message as ActionRequestMessage).payload?.message || "Action required"}</div>
              </div>
            )}

            {/* Display bubbles */}
            {message.type === "bubbles" && (
              <div className="space-y-2">
                {(message as BubblesMessage).payload?.bubbles?.map((bubble, index) => (
                  <button
                    key={index}
                    className="inline-block bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-full px-4 py-2 text-sm mr-2 mb-2 transition-colors"
                    onClick={() => {
                      // Handle bubble click - could send the bubble value as a message
                      console.log("Bubble clicked:", bubble);
                    }}
                  >
                    {bubble.text}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
