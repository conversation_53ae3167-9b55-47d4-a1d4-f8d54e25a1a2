"use client";

import Image from "next/image";
import { Conversation } from "@/services/conversation-service";
import { formatDistanceToNow } from "date-fns";

interface ConversationMessageProps {
  conversation: Conversation;
}

export function ConversationMessage({ conversation }: ConversationMessageProps) {
  const isOperatorMessage = conversation.sender_type === "OPERATOR" || conversation.sender_type === "operator";
  const isUserMessage = conversation.sender_type === "USER" || conversation.sender_type === "demand";

  let messageContent = conversation.message || "";

  if (conversation.payload && conversation.message_type === "escalation_req") {
    console.log("escalation_req payload:", conversation.payload);
    try {
      const payload = JSON.parse(conversation.payload);
      if (payload.message) {
        messageContent = payload.message;
      }
    } catch (error) {
      console.warn("Failed to parse conversation payload:", error);
    }
  }

  if (!messageContent.trim()) {
    console.log("No message content found for conversation:", conversation);
    return null;
  }

  return (
    <div className="flex w-[870px] items-start gap-[20px]">
      <div className="w-[27px] h-[27px] flex-shrink-0">
        {isUserMessage && <Image src="/user-logo.svg" alt="User" width={27} height={27} />}
        {isOperatorMessage && <Image src="/ai-logo.svg" alt="Operator" width={27} height={27} />}
      </div>

      {/* Message Content */}
      <div className="w-full space-y-2">
        <div
          className="w-full dm-sans"
          style={{
            minHeight: "46px",
            fontStyle: "normal",
            fontFamily: "DM Sans",
            fontWeight: 400,
            fontSize: "18px",
            lineHeight: "23px",
            color: "#1D1D1B",
          }}
        >
          {messageContent}
        </div>

        {/* Timestamp */}
        <div className="text-xs text-gray-500">{formatDistanceToNow(new Date(conversation.created_at), { addSuffix: true })}</div>
      </div>
    </div>
  );
}
