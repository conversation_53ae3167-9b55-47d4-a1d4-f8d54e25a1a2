'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Define the field structure based on the provided data
export interface FormField {
  id: string;
  brandId: string;
  brandCode: string;
  phaseKey: string;
  fieldKey: string;
  status: 'completed' | 'pending';
  value: string;
  lastUpdatedAt: string;
  createdAt: string;
  fieldName: string;
  fieldType: string;
  fieldLabel: string;
  helpText: string;
  validation: string;
  isRequired: boolean;
  orderIndex: number;
}

interface DynamicFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  formData: FormField[];
  title?: string;
  description?: string;
}

export function DynamicFormModal({
  isOpen,
  onClose,
  formData,
  title = 'Complete Your Profile',
  description = 'Please fill out the following information to proceed.',
}: DynamicFormModalProps) {
  // Initialize form values from the provided data
  const [formValues, setFormValues] = useState<Record<string, string>>(() => {
    const initialValues: Record<string, string> = {};
    formData.forEach((field) => {
      initialValues[field.fieldKey] = field.value || '';
    });
    return initialValues;
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [completedFields, setCompletedFields] = useState<string[]>(
    formData.filter((field) => field.status === 'completed').map((field) => field.fieldKey)
  );

  const handleInputChange = (fieldKey: string, value: string) => {
    setFormValues((prev) => ({
      ...prev,
      [fieldKey]: value,
    }));

    // Clear error when user starts typing
    if (errors[fieldKey]) {
      setErrors((prev) => ({
        ...prev,
        [fieldKey]: '',
      }));
    }
  };

  const validateField = (field: FormField, value: string): string => {
    try {
      const validationRules = JSON.parse(field.validation);

      if (validationRules.required && !value.trim()) {
        return `${field.fieldLabel} is required`;
      }

      if (value && validationRules.min_length && value.length < validationRules.min_length) {
        return `${field.fieldLabel} must be at least ${validationRules.min_length} characters`;
      }

      if (value && validationRules.max_length && value.length > validationRules.max_length) {
        return `${field.fieldLabel} cannot exceed ${validationRules.max_length} characters`;
      }

      if (value && validationRules.pattern) {
        try {
          // Handle escaped backslashes in the pattern
          const cleanPattern = validationRules.pattern.replace(/\\\\/g, '\\');
          const pattern = new RegExp(cleanPattern);
          if (!pattern.test(value)) {
            return `${field.fieldLabel} format is invalid`;
          }
        } catch (e) {
          console.error(`Invalid regex pattern for ${field.fieldKey}:`, e);
        }
      }

      if (field.fieldType === 'number') {
        const numValue = Number(value);
        if (validationRules.min !== undefined && numValue < validationRules.min) {
          return `${field.fieldLabel} must be at least ${validationRules.min}`;
        }
        if (validationRules.max !== undefined && numValue > validationRules.max) {
          return `${field.fieldLabel} cannot exceed ${validationRules.max}`;
        }
      }

      return '';
    } catch (error) {
      console.error(`Error validating field ${field.fieldKey}:`, error);
      return '';
    }
  };

  const handleSubmit = async () => {
    // Validate all fields
    const newErrors: Record<string, string> = {};
    let hasErrors = false;

    formData.forEach((field) => {
      const value = formValues[field.fieldKey] || '';
      const error = validateField(field, value);
      if (error) {
        newErrors[field.fieldKey] = error;
        hasErrors = true;
      }
    });

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    setIsSubmitting(true);

    try {
      // In a real app, you would submit the form data to your API
      console.log('Form data to submit:', formValues);

      // Update the completed fields
      const newCompletedFields = Object.keys(formValues).filter((key) => formValues[key]);
      setCompletedFields(newCompletedFields);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Close the modal on success
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderField = (field: FormField) => {
    const { fieldKey, fieldType, fieldLabel, helpText } = field;
    const value = formValues[fieldKey] || '';
    const error = errors[fieldKey];
    const isCompleted = completedFields.includes(fieldKey);

    // Add a visual indicator for completed fields
    const completedIndicator = isCompleted ? (
      <span className="text-green-500 text-xs ml-2">(Completed)</span>
    ) : null;

    switch (fieldType) {
      case 'textarea':
        return (
          <div key={fieldKey} className="space-y-2 mb-4">
            <Label htmlFor={fieldKey} className="text-sm font-medium flex items-center">
              {fieldLabel} {field.isRequired && <span className="text-red-500 ml-1">*</span>}
              {completedIndicator}
            </Label>
            <Textarea
              id={fieldKey}
              value={value}
              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
              placeholder={fieldLabel}
              className={error ? 'border-red-500' : isCompleted ? 'border-green-500' : ''}
              disabled={isSubmitting}
            />
            <p className="text-xs text-gray-500">{helpText}</p>
            {error && <p className="text-xs text-red-500">{error}</p>}
          </div>
        );

      case 'select':
        try {
          const validationObj = JSON.parse(field.validation);
          const options = validationObj.options || [];

          return (
            <div key={fieldKey} className="space-y-2 mb-4">
              <Label htmlFor={fieldKey} className="text-sm font-medium flex items-center">
                {fieldLabel} {field.isRequired && <span className="text-red-500 ml-1">*</span>}
                {completedIndicator}
              </Label>
              <Select
                value={value}
                onValueChange={(value) => handleInputChange(fieldKey, value)}
                disabled={isSubmitting}
              >
                <SelectTrigger
                  id={fieldKey}
                  className={error ? 'border-red-500' : isCompleted ? 'border-green-500' : ''}
                >
                  <SelectValue placeholder={`Select ${fieldLabel}`} />
                </SelectTrigger>
                <SelectContent>
                  {options.map((option: { value: string; label: string }) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">{helpText}</p>
              {error && <p className="text-xs text-red-500">{error}</p>}
            </div>
          );
        } catch (e) {
          console.error(`Error parsing options for ${fieldKey}:`, e);
          return null;
        }

      case 'number':
        return (
          <div key={fieldKey} className="space-y-2 mb-4">
            <Label htmlFor={fieldKey} className="text-sm font-medium flex items-center">
              {fieldLabel} {field.isRequired && <span className="text-red-500 ml-1">*</span>}
              {completedIndicator}
            </Label>
            <Input
              id={fieldKey}
              type="number"
              value={value}
              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
              placeholder={fieldLabel}
              className={error ? 'border-red-500' : isCompleted ? 'border-green-500' : ''}
              disabled={isSubmitting}
            />
            <p className="text-xs text-gray-500">{helpText}</p>
            {error && <p className="text-xs text-red-500">{error}</p>}
          </div>
        );

      case 'email':
        return (
          <div key={fieldKey} className="space-y-2 mb-4">
            <Label htmlFor={fieldKey} className="text-sm font-medium flex items-center">
              {fieldLabel} {field.isRequired && <span className="text-red-500 ml-1">*</span>}
              {completedIndicator}
            </Label>
            <Input
              id={fieldKey}
              type="email"
              value={value}
              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
              placeholder={fieldLabel}
              className={error ? 'border-red-500' : isCompleted ? 'border-green-500' : ''}
              disabled={isSubmitting}
            />
            <p className="text-xs text-gray-500">{helpText}</p>
            {error && <p className="text-xs text-red-500">{error}</p>}
          </div>
        );

      case 'text':
      default:
        return (
          <div key={fieldKey} className="space-y-2 mb-4">
            <Label htmlFor={fieldKey} className="text-sm font-medium flex items-center">
              {fieldLabel} {field.isRequired && <span className="text-red-500 ml-1">*</span>}
              {completedIndicator}
            </Label>
            <Input
              id={fieldKey}
              type="text"
              value={value}
              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
              placeholder={fieldLabel}
              className={error ? 'border-red-500' : isCompleted ? 'border-green-500' : ''}
              disabled={isSubmitting}
            />
            <p className="text-xs text-gray-500">{helpText}</p>
            {error && <p className="text-xs text-red-500">{error}</p>}
          </div>
        );
    }
  };

  // Calculate progress
  const totalFields = formData.length;
  const completedCount = completedFields.length;
  const progressPercentage = totalFields > 0 ? Math.round((completedCount / totalFields) * 100) : 0;

  // Sort fields by orderIndex
  const sortedFields = [...formData].sort((a, b) => a.orderIndex - b.orderIndex);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        {/* Progress bar */}
        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
          <div
            className="bg-green-600 h-2.5 rounded-full"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
        <div className="text-xs text-gray-500 mb-4">
          Progress: {completedCount} of {totalFields} fields completed ({progressPercentage}%)
        </div>

        <div className="py-4">{sortedFields.map(renderField)}</div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Save for Later
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="bg-[#01734e] hover:bg-[#015a3d]"
          >
            {isSubmitting ? 'Submitting...' : 'Submit'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
