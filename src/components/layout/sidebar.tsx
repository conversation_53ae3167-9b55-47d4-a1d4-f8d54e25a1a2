"use client";

import { usePathname } from "next/navigation";
import { MessageSquare, ChevronLeft } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { useSession } from "@/contexts/session-context";
import { EscalationSidebar } from "@/components/sidebar/escalation-sidebar";

export function Sidebar() {
  const pathname = usePathname();
  const [expanded, setExpanded] = useState(() => {
    // Check if we have a saved state in localStorage
    if (typeof window !== "undefined") {
      const savedState = localStorage.getItem("sidebarExpanded");
      // Default to true if no saved state
      return savedState === null ? true : savedState === "true";
    }
    return true;
  });

  const { user, isAuthenticated, isLoading } = useAuth();
  const { setCurrentEscalationSessionId } = useSession();

  // Define navigation items for operator app
  const navigation = [
    {
      name: "Assistant",
      href: "/assistant",
      icon: MessageSquare,
      current: pathname === "/assistant",
      requiresAuth: false,
    },
  ];

  // Filter navigation items based on authentication status
  const filteredNavigation = navigation.filter((item) => !item.requiresAuth || (item.requiresAuth && isAuthenticated));

  return (
    <>
      <div
        className={`flex h-full flex-col bg-white transition-all duration-300 ${
          expanded ? "w-64 border-r border-gray-200" : "w-[61px] border-r border-[rgba(29,29,27,0.06)] filter drop-shadow-[2px_4px_4px_rgba(1,1,1,0.1)]"
        }`}
      >
        <div className="h-16 shrink-0 relative">
          <div
            className="absolute left-0 top-0 h-full flex items-center px-4 cursor-pointer"
            onClick={() => {
              if (!expanded) {
                setExpanded(true);
                localStorage.setItem("sidebarExpanded", "true");
              }
            }}
          >
            {expanded ? (
              <Image src="/klub-full-logo.svg" alt="Klub Logo" width={100} height={40} style={{ objectFit: "contain" }} />
            ) : (
              <Image
                src="/klub-k-logo.svg"
                alt="Klub Logo"
                width={21}
                height={29}
                style={{
                  position: "absolute",
                  left: "20px",
                  top: "34px",
                  objectFit: "contain",
                }}
              />
            )}
          </div>

          {expanded && (
            <div className="absolute right-2 top-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setExpanded(false);
                  localStorage.setItem("sidebarExpanded", "false");
                }}
              >
                <ChevronLeft size={18} />
              </Button>
            </div>
          )}
        </div>

        <nav className="flex-shrink-0 pt-5">
          <ul className="flex flex-col gap-y-4 px-2">
            {filteredNavigation.map((item) => (
              <li key={item.name}>
                <div
                  onClick={() => {
                    window.location.href = item.href;
                  }}
                  className={`
                    flex items-center gap-x-3 rounded-md p-2 text-sm font-medium cursor-pointer
                    ${expanded ? "" : "justify-center"}
                    ${item.current ? "bg-gray-100 text-primary" : "text-gray-700 hover:bg-gray-50 hover:text-primary"}
                  `}
                  title={!expanded ? item.name : undefined}
                >
                  {item.name === "Assistant" ? (
                    <Image src="/sidebar-ai-logo.svg" alt="AI Logo" width={24} height={24} />
                  ) : (
                    <item.icon className={`h-5 w-5 ${item.current ? "text-primary" : "text-gray-400"}`} aria-hidden="true" />
                  )}
                  {expanded && <span>{item.name === "Assistant" ? "Klub AI" : item.name}</span>}
                </div>
              </li>
            ))}
          </ul>
        </nav>

        {/* Escalation Sidebar - only show when authenticated */}
        <div className="flex-1 min-h-0 mt-4">
          {isAuthenticated && (
            <EscalationSidebar
              expanded={expanded}
              onTaskClick={(sessionId) => {
                setCurrentEscalationSessionId(sessionId);
                // Navigate to assistant page if not already there
                if (pathname !== "/assistant") {
                  window.location.href = "/assistant";
                }
              }}
            />
          )}
        </div>

        <div className="p-4 flex-shrink-0">
          {isLoading ? (
            // Loading state - show only the icon without any text
            <Button variant="ghost" className={`w-full flex items-center gap-2 ${expanded ? "justify-start" : "justify-center"}`}>
              <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                <Image src="/profile.svg" alt="Profile" width={24} height={24} />
              </div>
              {expanded && (
                <div className="flex flex-col items-start text-sm">
                  <span className="font-medium text-gray-400">Loading...</span>
                </div>
              )}
            </Button>
          ) : isAuthenticated ? (
            // Authenticated user - show profile info without logout option
            <div className={`w-full flex items-center gap-2 p-2 ${expanded ? "justify-start" : "justify-center"}`}>
              <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                <Image src="/profile.svg" alt="Profile" width={24} height={24} />
              </div>
              {expanded && (
                <div className="flex flex-col items-start text-sm">
                  <span className="font-medium">{user?.name}</span>
                  <span className="text-xs text-gray-500">{user?.email}</span>
                </div>
              )}
            </div>
          ) : (
            // Non-authenticated user - show guest without sign in option
            <div className={`w-full flex items-center gap-2 p-2 ${expanded ? "justify-start" : "justify-center"}`}>
              <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                <Image src="/profile.svg" alt="Profile" width={24} height={24} />
              </div>
              {expanded && <span className="text-sm text-gray-500">Guest User</span>}
            </div>
          )}
        </div>
      </div>
    </>
  );
}
