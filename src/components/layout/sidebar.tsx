"use client";

import { usePathname } from "next/navigation";
import { useState } from "react";
import { Menu, X } from "lucide-react";
import Image from "next/image";
import { useAuth } from "@/hooks/use-auth";
import { useSession } from "@/contexts/session-context";
import { EscalationSidebar } from "@/components/sidebar/escalation-sidebar";

export function Sidebar() {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user, isAuthenticated, isLoading } = useAuth();
  const { setCurrentEscalationSessionId } = useSession();

  return (
    <>
      {/* Mobile menu button */}
      <div className="md:hidden fixed top-4 left-4 z-50">
        <button onClick={() => setMobileMenuOpen(!mobileMenuOpen)} className="p-2 bg-white border border-gray-200 rounded-md shadow-sm">
          {mobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
        </button>
      </div>

      {/* Mobile sidebar overlay */}
      {mobileMenuOpen && <div className="md:hidden fixed inset-0 z-40 bg-black bg-opacity-50" onClick={() => setMobileMenuOpen(false)} />}

      {/* Sidebar */}
      <div
        className={`
        ${mobileMenuOpen ? "translate-x-0" : "-translate-x-full"}
        md:translate-x-0 md:static fixed inset-y-0 left-0 z-40
        flex h-full flex-col bg-white w-64 border-r border-gray-200
        transition-transform duration-300 ease-in-out
      `}
      >
        {/* Header with logo */}
        <div className="h-16 shrink-0 flex items-center px-4">
          <Image src="/klub-full-logo.svg" alt="Klub Logo" width={100} height={40} style={{ objectFit: "contain" }} />
        </div>

        {/* Escalation Sidebar - only show when authenticated */}
        <div className="flex-1 min-h-0 pt-5">
          {isAuthenticated && (
            <EscalationSidebar
              onTaskClick={(sessionId) => {
                setCurrentEscalationSessionId(sessionId);
                // Navigate to assistant page if not already there
                if (pathname !== "/assistant") {
                  window.location.href = "/assistant";
                }
              }}
            />
          )}
        </div>

        {/* User profile section */}
        <div className="p-4 flex-shrink-0">
          {isLoading ? (
            <div className="w-full flex items-center gap-2 justify-start">
              <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                <Image src="/profile.svg" alt="Profile" width={24} height={24} />
              </div>
              <div className="flex flex-col items-start text-sm">
                <span className="font-medium text-gray-400">Loading...</span>
              </div>
            </div>
          ) : isAuthenticated ? (
            <div className="w-full flex items-center gap-2 p-2 justify-start">
              <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                <Image src="/profile.svg" alt="Profile" width={24} height={24} />
              </div>
              <div className="flex flex-col items-start text-sm">
                <span className="font-medium">{user?.name}</span>
                <span className="text-xs text-gray-500">{user?.email}</span>
              </div>
            </div>
          ) : (
            <div className="w-full flex items-center gap-2 p-2 justify-start">
              <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                <Image src="/profile.svg" alt="Profile" width={24} height={24} />
              </div>
              <span className="text-sm text-gray-500">Guest User</span>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
