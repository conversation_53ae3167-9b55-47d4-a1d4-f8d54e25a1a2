"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";

export function useOperatorSession() {
  const [sessionId, setSessionId] = useState<string>("");
  const [operatorId, setOperatorId] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    const initializeSession = () => {
      try {
        // Only proceed if user is authenticated
        if (!isAuthenticated) {
          setSessionId("");
          setOperatorId("");
          setIsLoading(false);
          return;
        }

        // Get existing session_id from localStorage
        const existingSessionId = localStorage.getItem("session_id");
        if (existingSessionId) {
          setSessionId(existingSessionId);
        }

        // Get existing operator_id from localStorage
        const existingOperatorId = localStorage.getItem("operator_id");
        if (existingOperatorId) {
          setOperatorId(existingOperatorId);
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Error initializing operator session:", error);
        setIsLoading(false);
      }
    };

    initializeSession();
  }, [isAuthenticated]);

  // Function to update session ID (called when receiving init message)
  const updateSessionId = useCallback((newSessionId: string) => {
    try {
      setSessionId(newSessionId);
      localStorage.setItem("session_id", newSessionId);
      console.log("Session ID updated:", newSessionId);
    } catch (error) {
      console.error("Error updating session ID:", error);
    }
  }, []);

  // Function to update operator ID (called on successful session_start_resp)
  const updateOperatorId = useCallback((newOperatorId: string) => {
    try {
      setOperatorId(newOperatorId);
      localStorage.setItem("operator_id", newOperatorId);
      console.log("Operator ID updated:", newOperatorId);
    } catch (error) {
      console.error("Error updating operator ID:", error);
    }
  }, []);

  // Function to clear session data (called on error or logout)
  const clearSession = useCallback(() => {
    try {
      setSessionId("");
      setOperatorId("");
      localStorage.removeItem("session_id");
      localStorage.removeItem("operator_id");
      console.log("Session data cleared");
    } catch (error) {
      console.error("Error clearing session data:", error);
    }
  }, []);

  // Function to get WebSocket endpoint
  const getWebSocketEndpoint = useCallback(() => {
    const baseWsUrl = process.env.NEXT_PUBLIC_WEBSOCKET_URL;
    if (!baseWsUrl) {
      throw new Error("WebSocket URL not defined in environment variables");
    }

    // Use /operator/:sessionId if we have a session_id, otherwise /operator
    if (sessionId) {
      return `${baseWsUrl}operator/${sessionId}`;
    } else {
      return `${baseWsUrl}operator`;
    }
  }, [sessionId]);

  return {
    sessionId,
    operatorId,
    isLoading,
    isAuthenticated,
    updateSessionId,
    updateOperatorId,
    clearSession,
    getWebSocketEndpoint,
  };
}
