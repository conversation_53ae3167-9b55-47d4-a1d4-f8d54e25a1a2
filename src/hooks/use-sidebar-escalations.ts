import { useState, useEffect, useCallback } from "react";
import { escalationService } from "@/services/escalation-service";
import { useAuth } from "@/hooks/use-auth";
import { Escalation } from "@/types/escalation";
import { usePagination } from "@/hooks/use-pagination";

// Define the event locally to avoid circular imports
const SIDEBAR_REFRESH_EVENT = "escalation-sidebar-refresh";

export function useSidebarEscalations() {
  const { user, isAuthenticated } = useAuth();

  const assignedTasksPagination = usePagination<Escalation>({
    initialPage: 1,
    pageSize: 10,
  });

  const pendingTasksPagination = usePagination<Escalation>({
    initialPage: 1,
    pageSize: 10,
  });

  const [error, setError] = useState<string | null>(null);

  const fetchAssignedTasks = useCallback(
    async (page: number, pageSize: number) => {
      if (!isAuthenticated || !user?.id) {
        return { items: [], totalCount: 0 };
      }

      const response = await escalationService.getEscalations({
        assigned_operator_id: user.id,
        status: "assigned",
        page,
        pageSize,
      });

      return {
        items: response.escalations,
        totalCount: response.total_count,
      };
    },
    [isAuthenticated, user?.id]
  );

  const fetchPendingTasks = useCallback(
    async (page: number, pageSize: number) => {
      if (!isAuthenticated || !user?.id) {
        return { items: [], totalCount: 0 };
      }

      const response = await escalationService.getPendingEscalations(page, pageSize);

      return {
        items: response.escalations,
        totalCount: response.total_count,
      };
    },
    [isAuthenticated, user?.id]
  );

  const loadInitialData = useCallback(async () => {
    console.log("🔄 loadInitialData called - isAuthenticated:", isAuthenticated, "userId:", user?.id);

    if (!isAuthenticated || !user?.id) {
      console.log("❌ Not authenticated or no user ID, resetting pagination");
      assignedTasksPagination.reset();
      pendingTasksPagination.reset();
      setError(null);
      return;
    }

    try {
      console.log("📡 Fetching escalation data...");
      setError(null);
      await Promise.all([assignedTasksPagination.loadInitial(fetchAssignedTasks), pendingTasksPagination.loadInitial(fetchPendingTasks)]);
      console.log("✅ Escalation data fetched successfully");
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch escalation data";
      setError(errorMessage);
      console.error("❌ Error fetching sidebar escalation data:", err);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, user?.id, fetchAssignedTasks, fetchPendingTasks]);

  // Auto-refresh every 30 seconds - only depend on auth state
  useEffect(() => {
    console.log("🕐 Auto-refresh useEffect triggered");

    if (!isAuthenticated || !user?.id) {
      console.log("❌ Not authenticated, skipping auto-refresh setup");
      return;
    }

    // Initial load
    loadInitialData();

    const interval = setInterval(() => {
      console.log("⏰ Auto-refresh interval triggered (30s)");
      loadInitialData();
    }, 30000);

    return () => {
      console.log("🧹 Cleaning up auto-refresh interval");
      clearInterval(interval);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, user?.id]);

  useEffect(() => {
    console.log("📡 Event listener useEffect triggered");
    const handleSidebarRefresh = () => {
      console.log("🔄 Sidebar refresh event received");
      loadInitialData();
    };

    if (typeof window !== "undefined") {
      window.addEventListener(SIDEBAR_REFRESH_EVENT, handleSidebarRefresh);
      return () => {
        console.log("🧹 Cleaning up sidebar refresh event listener");
        window.removeEventListener(SIDEBAR_REFRESH_EVENT, handleSidebarRefresh);
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Assign pending task to current user
  const assignTask = useCallback(
    async (taskId: string) => {
      if (!user?.id) return;

      try {
        await escalationService.assignEscalation(taskId, user.id);
        loadInitialData();
      } catch (error) {
        console.error("Failed to assign task:", error);
        setError(error instanceof Error ? error.message : "Failed to assign task");
      }
    },
    [user?.id, loadInitialData]
  );

  // Resolve assigned task
  const resolveTask = useCallback(
    async (taskId: string) => {
      if (!user?.id) return;

      try {
        const escalationToResolve = assignedTasksPagination.items.find((task) => task.id === taskId) || pendingTasksPagination.items.find((task) => task.id === taskId);

        const resolvedEscalation = await escalationService.resolveEscalation(taskId, user.id);

        if (typeof window !== "undefined" && escalationToResolve) {
          const event = new CustomEvent("escalation-resolved", {
            detail: {
              escalationId: taskId,
              sessionId: escalationToResolve.session_id,
              resolvedEscalation,
            },
          });
          window.dispatchEvent(event);
        }

        loadInitialData();
      } catch (error) {
        console.error("Failed to resolve task:", error);
        setError(error instanceof Error ? error.message : "Failed to resolve task");
      }
    },
    [user?.id, loadInitialData, assignedTasksPagination.items, pendingTasksPagination.items]
  );

  return {
    assignedTasks: assignedTasksPagination.items,
    pendingTasks: pendingTasksPagination.items,
    assignedCount: assignedTasksPagination.totalCount,
    pendingCount: pendingTasksPagination.totalCount,
    isLoading: assignedTasksPagination.isLoading || pendingTasksPagination.isLoading,
    isLoadingMoreAssigned: assignedTasksPagination.isLoadingMore,
    isLoadingMorePending: pendingTasksPagination.isLoadingMore,
    hasMoreAssigned: assignedTasksPagination.hasMore,
    hasMorePending: pendingTasksPagination.hasMore,
    error: error || assignedTasksPagination.error || pendingTasksPagination.error,
    refresh: loadInitialData,
    loadMoreAssigned: () => assignedTasksPagination.loadMore(fetchAssignedTasks),
    loadMorePending: () => pendingTasksPagination.loadMore(fetchPendingTasks),
    assignTask,
    resolveTask,
  };
}

// Hook for triggering refresh from other components
export function useEscalationRefresh() {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const triggerRefresh = useCallback(() => {
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  return {
    refreshTrigger,
    triggerRefresh,
  };
}
