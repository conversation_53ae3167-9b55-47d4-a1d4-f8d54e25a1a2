import { useState, useEffect, useCallback } from "react";
import { escalationService } from "@/services/escalation-service";
import { useAuth } from "@/hooks/use-auth";
import { Escalation } from "@/types/escalation";

// Define the event locally to avoid circular imports
const SIDEBAR_REFRESH_EVENT = "escalation-sidebar-refresh";

interface SidebarEscalationData {
  assignedTasks: Escalation[];
  pendingTasks: Escalation[];
  assignedCount: number;
  pendingCount: number;
  isLoading: boolean;
  error: string | null;
}

export function useSidebarEscalations() {
  const { user, isAuthenticated } = useAuth();
  const [data, setData] = useState<SidebarEscalationData>({
    assignedTasks: [],
    pendingTasks: [],
    assignedCount: 0,
    pendingCount: 0,
    isLoading: false,
    error: null,
  });

  const fetchEscalationData = useCallback(async () => {
    if (!isAuthenticated || !user?.id) {
      setData((prev) => ({
        ...prev,
        assignedTasks: [],
        nextPendingTask: null,
        assignedCount: 0,
        isLoading: false,
        error: null,
      }));
      return;
    }

    setData((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // Fetch assigned tasks (assigned to current user)
      const assignedResponse = await escalationService.getEscalations({
        assigned_operator_id: user.id,
        status: "assigned",
        page: 1,
        pageSize: 10, // Limit for sidebar display
      });

      // Fetch pending tasks
      const pendingResponse = await escalationService.getPendingEscalations(1, 10);

      setData({
        assignedTasks: assignedResponse.escalations,
        pendingTasks: pendingResponse.escalations,
        assignedCount: assignedResponse.total_count,
        pendingCount: pendingResponse.total_count,
        isLoading: false,
        error: null,
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch escalation data";
      setData((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      console.error("Error fetching sidebar escalation data:", err);
    }
  }, [isAuthenticated, user?.id]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    fetchEscalationData();

    const interval = setInterval(fetchEscalationData, 30000);
    return () => clearInterval(interval);
  }, [fetchEscalationData]);

  // Refresh when user authentication changes
  useEffect(() => {
    fetchEscalationData();
  }, [isAuthenticated, user?.id, fetchEscalationData]);

  // Listen for sidebar refresh events
  useEffect(() => {
    const handleSidebarRefresh = () => {
      fetchEscalationData();
    };

    if (typeof window !== "undefined") {
      window.addEventListener(SIDEBAR_REFRESH_EVENT, handleSidebarRefresh);
      return () => {
        window.removeEventListener(SIDEBAR_REFRESH_EVENT, handleSidebarRefresh);
      };
    }
  }, [fetchEscalationData]);

  // Assign pending task to current user
  const assignTask = useCallback(
    async (taskId: string) => {
      if (!user?.id) return;

      try {
        await escalationService.assignEscalation(taskId, user.id);
        // Refresh data after assignment
        fetchEscalationData();
      } catch (error) {
        console.error("Failed to assign task:", error);
        setData((prev) => ({
          ...prev,
          error: error instanceof Error ? error.message : "Failed to assign task",
        }));
      }
    },
    [user?.id, fetchEscalationData]
  );

  // Resolve assigned task
  const resolveTask = useCallback(
    async (taskId: string) => {
      if (!user?.id) return;

      try {
        await escalationService.resolveEscalation(taskId, user.id);
        // Refresh data after resolution
        fetchEscalationData();
      } catch (error) {
        console.error("Failed to resolve task:", error);
        setData((prev) => ({
          ...prev,
          error: error instanceof Error ? error.message : "Failed to resolve task",
        }));
      }
    },
    [user?.id, fetchEscalationData]
  );

  return {
    ...data,
    refresh: fetchEscalationData,
    assignTask,
    resolveTask,
  };
}

// Hook for triggering refresh from other components
export function useEscalationRefresh() {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const triggerRefresh = useCallback(() => {
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  return {
    refreshTrigger,
    triggerRefresh,
  };
}
