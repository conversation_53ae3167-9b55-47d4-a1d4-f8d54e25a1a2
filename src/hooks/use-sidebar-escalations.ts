import { useState, useEffect, useCallback, useRef } from "react";
import { escalationService } from "@/services/escalation-service";
import { useAuth } from "@/hooks/use-auth";
import { Escalation } from "@/types/escalation";
import { usePagination } from "@/hooks/use-pagination";

// Define the event locally to avoid circular imports
const SIDEBAR_REFRESH_EVENT = "escalation-sidebar-refresh";

export function useSidebarEscalations() {
  const { user, isAuthenticated } = useAuth();

  const assignedTasksPagination = usePagination<Escalation>({
    initialPage: 1,
    pageSize: 10,
  });

  const pendingTasksPagination = usePagination<Escalation>({
    initialPage: 1,
    pageSize: 10,
  });

  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Create stable references to avoid dependency issues
  const paginationRef = useRef({
    assignedTasksPagination,
    pendingTasksPagination,
  });

  const authRef = useRef({ isAuthenticated, userId: user?.id });

  // Update refs when pagination objects change
  paginationRef.current = {
    assignedTasksPagination,
    pendingTasksPagination,
  };

  // Update auth ref
  authRef.current = { isAuthenticated, userId: user?.id };

  const fetchAssignedTasks = useCallback(
    async (page: number, pageSize: number) => {
      const { isAuthenticated: authStatus, userId } = authRef.current;
      if (!authStatus || !userId) {
        return { items: [], totalCount: 0 };
      }

      const response = await escalationService.getEscalations({
        assigned_operator_id: userId,
        status: "assigned",
        page,
        pageSize,
      });

      return {
        items: response.escalations,
        totalCount: response.total_count,
      };
    },
    [] // No dependencies - uses refs
  );

  const fetchPendingTasks = useCallback(
    async (page: number, pageSize: number) => {
      const { isAuthenticated: authStatus, userId } = authRef.current;
      if (!authStatus || !userId) {
        return { items: [], totalCount: 0 };
      }

      const response = await escalationService.getPendingEscalations(page, pageSize);

      return {
        items: response.escalations,
        totalCount: response.total_count,
      };
    },
    [] // No dependencies - uses refs
  );

  // Single effect to handle initial load and auth changes
  useEffect(() => {
    console.log("SIDEBAR_DEBUG: 🔄 Auth useEffect triggered - isAuthenticated:", isAuthenticated, "userId:", user?.id, "isInitialized:", isInitialized);

    if (!isAuthenticated || !user?.id) {
      console.log("SIDEBAR_DEBUG: ❌ Not authenticated or no user ID, resetting pagination");
      paginationRef.current.assignedTasksPagination.reset();
      paginationRef.current.pendingTasksPagination.reset();
      setError(null);
      setIsInitialized(false);
      return;
    }

    if (isInitialized) {
      console.log("SIDEBAR_DEBUG: ✅ Already initialized, skipping data load");
      return;
    }

    const loadData = async () => {
      try {
        console.log("SIDEBAR_DEBUG: 📡 Fetching escalation data...");
        setError(null);
        await Promise.all([paginationRef.current.assignedTasksPagination.loadInitial(fetchAssignedTasks), paginationRef.current.pendingTasksPagination.loadInitial(fetchPendingTasks)]);
        console.log("SIDEBAR_DEBUG: ✅ Escalation data fetched successfully");
        setIsInitialized(true);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to fetch escalation data";
        setError(errorMessage);
        console.error("SIDEBAR_DEBUG: ❌ Error fetching sidebar escalation data:", err);
      }
    };

    loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, user?.id, isInitialized]); // Added isInitialized back to dependencies

  // Separate effect for auto-refresh interval
  useEffect(() => {
    if (!isInitialized || !isAuthenticated || !user?.id) {
      console.log("SIDEBAR_DEBUG: 🕐 Auto-refresh skipped - not ready");
      return;
    }

    console.log("SIDEBAR_DEBUG: 🕐 Setting up auto-refresh interval");

    const interval = setInterval(async () => {
      console.log("SIDEBAR_DEBUG: ⏰ Auto-refresh interval triggered (30s)");
      try {
        await Promise.all([paginationRef.current.assignedTasksPagination.loadInitial(fetchAssignedTasks), paginationRef.current.pendingTasksPagination.loadInitial(fetchPendingTasks)]);
        console.log("SIDEBAR_DEBUG: ✅ Auto-refresh completed");
      } catch (err) {
        console.error("SIDEBAR_DEBUG: ❌ Auto-refresh failed:", err);
      }
    }, 30000);

    return () => {
      console.log("SIDEBAR_DEBUG: 🧹 Cleaning up auto-refresh interval");
      clearInterval(interval);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized, isAuthenticated, user?.id]); // Fetch functions are stable via useCallback

  // Event listener effect (only set up once)
  useEffect(() => {
    console.log("SIDEBAR_DEBUG: 📡 Setting up event listener");

    const handleSidebarRefresh = async () => {
      console.log("SIDEBAR_DEBUG: 🔄 Sidebar refresh event received");
      if (!isAuthenticated || !user?.id) return;

      try {
        await Promise.all([paginationRef.current.assignedTasksPagination.loadInitial(fetchAssignedTasks), paginationRef.current.pendingTasksPagination.loadInitial(fetchPendingTasks)]);
        console.log("SIDEBAR_DEBUG: ✅ Event refresh completed");
      } catch (err) {
        console.error("SIDEBAR_DEBUG: ❌ Event refresh failed:", err);
      }
    };

    if (typeof window !== "undefined") {
      window.addEventListener(SIDEBAR_REFRESH_EVENT, handleSidebarRefresh);
      return () => {
        console.log("SIDEBAR_DEBUG: 🧹 Cleaning up sidebar refresh event listener");
        window.removeEventListener(SIDEBAR_REFRESH_EVENT, handleSidebarRefresh);
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, user?.id]); // Fetch functions are stable via useCallback

  const loadInitialData = useCallback(async () => {
    if (!isAuthenticated || !user?.id) return;

    try {
      setError(null);
      await Promise.all([paginationRef.current.assignedTasksPagination.loadInitial(fetchAssignedTasks), paginationRef.current.pendingTasksPagination.loadInitial(fetchPendingTasks)]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch escalation data";
      setError(errorMessage);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, user?.id]); // Fetch functions are stable via useCallback

  // Assign pending task to current user
  const assignTask = useCallback(
    async (taskId: string) => {
      if (!user?.id) return;

      try {
        await escalationService.assignEscalation(taskId, user.id);
        loadInitialData();
      } catch (error) {
        console.error("Failed to assign task:", error);
        setError(error instanceof Error ? error.message : "Failed to assign task");
      }
    },
    [user?.id, loadInitialData]
  );

  // Resolve assigned task
  const resolveTask = useCallback(
    async (taskId: string) => {
      if (!user?.id) return;

      try {
        const escalationToResolve = assignedTasksPagination.items.find((task) => task.id === taskId) || pendingTasksPagination.items.find((task) => task.id === taskId);

        const resolvedEscalation = await escalationService.resolveEscalation(taskId, user.id);

        if (typeof window !== "undefined" && escalationToResolve) {
          const event = new CustomEvent("escalation-resolved", {
            detail: {
              escalationId: taskId,
              sessionId: escalationToResolve.session_id,
              resolvedEscalation,
            },
          });
          window.dispatchEvent(event);
        }

        loadInitialData();
      } catch (error) {
        console.error("Failed to resolve task:", error);
        setError(error instanceof Error ? error.message : "Failed to resolve task");
      }
    },
    [user?.id, loadInitialData, assignedTasksPagination.items, pendingTasksPagination.items]
  );

  return {
    assignedTasks: assignedTasksPagination.items,
    pendingTasks: pendingTasksPagination.items,
    assignedCount: assignedTasksPagination.totalCount,
    pendingCount: pendingTasksPagination.totalCount,
    isLoading: assignedTasksPagination.isLoading || pendingTasksPagination.isLoading,
    isLoadingMoreAssigned: assignedTasksPagination.isLoadingMore,
    isLoadingMorePending: pendingTasksPagination.isLoadingMore,
    hasMoreAssigned: assignedTasksPagination.hasMore,
    hasMorePending: pendingTasksPagination.hasMore,
    error: error || assignedTasksPagination.error || pendingTasksPagination.error,
    refresh: loadInitialData,
    loadMoreAssigned: () => assignedTasksPagination.loadMore(fetchAssignedTasks),
    loadMorePending: () => pendingTasksPagination.loadMore(fetchPendingTasks),
    assignTask,
    resolveTask,
  };
}

// Hook for triggering refresh from other components
export function useEscalationRefresh() {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const triggerRefresh = useCallback(() => {
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  return {
    refreshTrigger,
    triggerRefresh,
  };
}
