import { useState, useEffect, useCallback } from "react";
import { escalationService } from "@/services/escalation-service";
import { useAuth } from "@/hooks/use-auth";
import { Escalation } from "@/types/escalation";

// Define the event locally to avoid circular imports
const SIDEBAR_REFRESH_EVENT = "escalation-sidebar-refresh";

export function useSidebarEscalations() {
  const { user, isAuthenticated } = useAuth();

  const [assignedTasks, setAssignedTasks] = useState<Escalation[]>([]);
  const [pendingTasks, setPendingTasks] = useState<Escalation[]>([]);
  const [assignedCount, setAssignedCount] = useState(0);
  const [pendingCount, setPendingCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const fetchAssignedTasks = useCallback(async () => {
    if (!isAuthenticated || !user?.id) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await escalationService.getEscalations({
        assigned_operator_id: user.id,
        status: "assigned",
        page: 1,
        pageSize: 100,
      });

      setAssignedTasks(response.escalations);
      setAssignedCount(response.total_count);
    } catch (err) {
      console.error("SIDEBAR_DEBUG: ❌ Error fetching assigned tasks:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch assigned tasks");
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user?.id]);

  const fetchPendingTasks = useCallback(async () => {
    if (!isAuthenticated || !user?.id) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await escalationService.getPendingEscalations(1, 100);

      setPendingTasks(response.escalations);
      setPendingCount(response.total_count);
    } catch (err) {
      console.error("SIDEBAR_DEBUG: ❌ Error fetching pending tasks:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch pending tasks");
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user?.id]);

  // Single effect to handle initial load and auth changes
  useEffect(() => {
    console.log("SIDEBAR_DEBUG: 🔄 Auth useEffect triggered - isAuthenticated:", isAuthenticated, "userId:", user?.id, "isInitialized:", isInitialized);

    if (!isAuthenticated || !user?.id) {
      console.log("SIDEBAR_DEBUG: ❌ Not authenticated or no user ID, resetting state");
      setAssignedTasks([]);
      setPendingTasks([]);
      setAssignedCount(0);
      setPendingCount(0);
      setError(null);
      setIsInitialized(false);
      return;
    }

    if (isInitialized) {
      console.log("SIDEBAR_DEBUG: ✅ Already initialized, skipping data load");
      return;
    }

    const loadData = async () => {
      try {
        console.log("SIDEBAR_DEBUG: 📡 Fetching escalation data...");
        setError(null);
        await Promise.all([fetchAssignedTasks(), fetchPendingTasks()]);
        console.log("SIDEBAR_DEBUG: ✅ Escalation data fetched successfully");
        setIsInitialized(true);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to fetch escalation data";
        setError(errorMessage);
        console.error("SIDEBAR_DEBUG: ❌ Error fetching sidebar escalation data:", err);
      }
    };

    loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, user?.id, isInitialized]); // Added isInitialized back to dependencies

  // Separate effect for auto-refresh interval
  useEffect(() => {
    if (!isInitialized || !isAuthenticated || !user?.id) {
      console.log("SIDEBAR_DEBUG: 🕐 Auto-refresh skipped - not ready");
      return;
    }

    console.log("SIDEBAR_DEBUG: 🕐 Setting up auto-refresh interval");

    const interval = setInterval(async () => {
      console.log("SIDEBAR_DEBUG: ⏰ Auto-refresh interval triggered (30s)");
      try {
        await Promise.all([fetchAssignedTasks(), fetchPendingTasks()]);
        console.log("SIDEBAR_DEBUG: ✅ Auto-refresh completed");
      } catch (err) {
        console.error("SIDEBAR_DEBUG: ❌ Auto-refresh failed:", err);
      }
    }, 30000);

    return () => {
      console.log("SIDEBAR_DEBUG: 🧹 Cleaning up auto-refresh interval");
      clearInterval(interval);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized, isAuthenticated, user?.id]); // Fetch functions are stable via useCallback

  // Event listener effect (only set up once)
  useEffect(() => {
    console.log("SIDEBAR_DEBUG: 📡 Setting up event listener");

    const handleSidebarRefresh = async () => {
      console.log("SIDEBAR_DEBUG: 🔄 Sidebar refresh event received");
      if (!isAuthenticated || !user?.id) return;

      try {
        await Promise.all([fetchAssignedTasks(), fetchPendingTasks()]);
        console.log("SIDEBAR_DEBUG: ✅ Event refresh completed");
      } catch (err) {
        console.error("SIDEBAR_DEBUG: ❌ Event refresh failed:", err);
      }
    };

    if (typeof window !== "undefined") {
      window.addEventListener(SIDEBAR_REFRESH_EVENT, handleSidebarRefresh);
      return () => {
        console.log("SIDEBAR_DEBUG: 🧹 Cleaning up sidebar refresh event listener");
        window.removeEventListener(SIDEBAR_REFRESH_EVENT, handleSidebarRefresh);
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, user?.id]); // Fetch functions are stable via useCallback

  const loadInitialData = useCallback(async () => {
    if (!isAuthenticated || !user?.id) return;

    try {
      setError(null);
      await Promise.all([fetchAssignedTasks(), fetchPendingTasks()]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch escalation data";
      setError(errorMessage);
    }
  }, [isAuthenticated, user?.id, fetchAssignedTasks, fetchPendingTasks]);

  // Assign pending task to current user
  const assignTask = useCallback(
    async (taskId: string) => {
      if (!user?.id) return;

      try {
        await escalationService.assignEscalation(taskId, user.id);
        loadInitialData();
      } catch (error) {
        console.error("Failed to assign task:", error);
        setError(error instanceof Error ? error.message : "Failed to assign task");
      }
    },
    [user?.id, loadInitialData]
  );

  // Resolve assigned task
  const resolveTask = useCallback(
    async (taskId: string) => {
      if (!user?.id) return;

      try {
        const escalationToResolve = assignedTasks.find((task: Escalation) => task.id === taskId) || pendingTasks.find((task: Escalation) => task.id === taskId);

        const resolvedEscalation = await escalationService.resolveEscalation(taskId, user.id);

        if (typeof window !== "undefined" && escalationToResolve) {
          const event = new CustomEvent("escalation-resolved", {
            detail: {
              escalationId: taskId,
              sessionId: escalationToResolve.session_id,
              resolvedEscalation,
            },
          });
          window.dispatchEvent(event);
        }

        loadInitialData();
      } catch (error) {
        console.error("Failed to resolve task:", error);
        setError(error instanceof Error ? error.message : "Failed to resolve task");
      }
    },
    [user?.id, loadInitialData, assignedTasks, pendingTasks]
  );

  return {
    assignedTasks,
    pendingTasks,
    assignedCount,
    pendingCount,
    isLoading,
    isLoadingMoreAssigned: false, // No pagination, so no "load more"
    isLoadingMorePending: false, // No pagination, so no "load more"
    hasMoreAssigned: false, // No pagination, so no "has more"
    hasMorePending: false, // No pagination, so no "has more"
    error,
    refresh: loadInitialData,
    loadMoreAssigned: () => {}, // No-op since we removed pagination
    loadMorePending: () => {}, // No-op since we removed pagination
    assignTask,
    resolveTask,
  };
}

// Hook for triggering refresh from other components
export function useEscalationRefresh() {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const triggerRefresh = useCallback(() => {
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  return {
    refreshTrigger,
    triggerRefresh,
  };
}
