import { useEffect, useCallback } from "react";
import { conversationService, Conversation, ConversationFilters } from "@/services/conversation-service";
import { usePagination } from "@/hooks/use-pagination";

interface UseConversationsOptions {
  sessionId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  filters?: ConversationFilters;
}

export function useConversations(options: UseConversationsOptions = {}) {
  const { sessionId, autoRefresh = false, refreshInterval = 10000, filters } = options;

  const conversationsPagination = usePagination<Conversation>({
    initialPage: 1,
    pageSize: 50,
  });

  const fetchConversationsPage = useCallback(
    async (page: number, pageSize: number) => {
      let response;

      if (sessionId) {
        response = await conversationService.getConversationsBySession(sessionId, page, pageSize);
      } else {
        response = await conversationService.getConversations({ ...filters, page, pageSize });
      }

      const filteredConversations = response.conversations.filter((conv) => {
        if (conv.message_type === "escalation_req") return true;
        if (conv.message_type === "text_req" && (conv.message || conv.payload)) return true;
        if (conv.message_type === "text_resp" && (conv.message || conv.payload)) return true;
        if (conv.message_type === "TEXT" && conv.sender_type !== "SYSTEM" && (conv.message || conv.payload)) return true;
        return false;
      });

      const sortedConversations = filteredConversations.sort((a, b) => {
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      });

      return {
        items: sortedConversations,
        totalCount: response.total_count,
      };
    },
    [sessionId, filters]
  );

  const loadInitialConversations = useCallback(async () => {
    try {
      await conversationsPagination.loadInitial(fetchConversationsPage);
    } catch (error) {
      console.error("Error loading conversations:", error);
    }
  }, [conversationsPagination, fetchConversationsPage]);

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(loadInitialConversations, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, loadInitialConversations]);

  const loadConversationsBySession = useCallback(
    async (newSessionId: string, append: boolean = false) => {
      const sessionFetchFn = async (p: number, pageSize: number) => {
        const response = await conversationService.getConversationsBySession(newSessionId, p, pageSize);

        const filteredConversations = response.conversations.filter((conv) => {
          if (conv.message_type === "escalation_req") return true;
          if (conv.message_type === "text_req" && (conv.message || conv.payload)) return true;
          if (conv.message_type === "text_resp" && (conv.message || conv.payload)) return true;
          if (conv.message_type === "TEXT" && conv.sender_type !== "SYSTEM" && (conv.message || conv.payload)) return true;
          return false;
        });

        const sortedConversations = filteredConversations.sort((a, b) => {
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        });

        return {
          items: sortedConversations,
          totalCount: response.total_count,
        };
      };

      if (append) {
        await conversationsPagination.loadMore(sessionFetchFn);
      } else {
        await conversationsPagination.loadInitial(sessionFetchFn);
      }
    },
    [conversationsPagination]
  );

  const loadMoreConversations = useCallback(
    async (sessionId: string) => {
      const sessionFetchFn = async (page: number, pageSize: number) => {
        const response = await conversationService.getConversationsBySession(sessionId, page, pageSize);

        const filteredConversations = response.conversations.filter((conv) => {
          if (conv.message_type === "escalation_req") return true;
          if (conv.message_type === "text_req" && (conv.message || conv.payload)) return true;
          if (conv.message_type === "text_resp" && (conv.message || conv.payload)) return true;
          if (conv.message_type === "TEXT" && conv.sender_type !== "SYSTEM" && (conv.message || conv.payload)) return true;
          return false;
        });

        const sortedConversations = filteredConversations.sort((a, b) => {
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        });

        return {
          items: sortedConversations,
          totalCount: response.total_count,
        };
      };

      await conversationsPagination.loadMore(sessionFetchFn);
    },
    [conversationsPagination]
  );

  const clearConversations = useCallback(() => {
    conversationsPagination.reset();
  }, [conversationsPagination]);

  // Create a scroll handler that uses the current session context
  const createScrollHandler = useCallback(
    (currentSessionId: string) => {
      const sessionFetchFn = async (page: number, pageSize: number) => {
        const response = await conversationService.getConversationsBySession(currentSessionId, page, pageSize);

        const filteredConversations = response.conversations.filter((conv) => {
          if (conv.message_type === "escalation_req") return true;
          if (conv.message_type === "text_req" && (conv.message || conv.payload)) return true;
          if (conv.message_type === "text_resp" && (conv.message || conv.payload)) return true;
          if (conv.message_type === "TEXT" && conv.sender_type !== "SYSTEM" && (conv.message || conv.payload)) return true;
          return false;
        });

        const sortedConversations = filteredConversations.sort((a, b) => {
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        });

        return {
          items: sortedConversations,
          totalCount: response.total_count,
        };
      };

      return (e: React.UIEvent<HTMLDivElement>) => conversationsPagination.handleScroll(e, sessionFetchFn, "top");
    },
    [conversationsPagination]
  );

  return {
    conversations: conversationsPagination.items,
    totalCount: conversationsPagination.totalCount,
    isLoading: conversationsPagination.isLoading,
    isLoadingMore: conversationsPagination.isLoadingMore,
    error: conversationsPagination.error,
    hasMore: conversationsPagination.hasMore,
    currentPage: conversationsPagination.currentPage,
    fetchConversations: loadInitialConversations,
    refreshConversations: loadInitialConversations,
    loadConversationsBySession,
    loadMoreConversations,
    clearConversations,
    createScrollHandler,
    // Keep the old handleScroll for backward compatibility
    handleScroll: (e: React.UIEvent<HTMLDivElement>) => conversationsPagination.handleScroll(e, fetchConversationsPage, "top"),
  };
}
