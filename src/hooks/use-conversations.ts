import { useState, useEffect, useCallback } from "react";
import { conversationService, Conversation, ConversationFilters } from "@/services/conversation-service";

interface UseConversationsOptions {
  sessionId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  filters?: ConversationFilters;
}

export function useConversations(options: UseConversationsOptions = {}) {
  const { sessionId, autoRefresh = false, refreshInterval = 10000, filters } = options;

  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchConversations = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      let response;

      if (sessionId) {
        response = await conversationService.getConversationsBySession(sessionId);
      } else {
        response = await conversationService.getConversations(filters);
      }

      setConversations(response.conversations);
      setTotalCount(response.total_count);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch conversations";
      setError(errorMessage);
      console.error("Error fetching conversations:", err);
    } finally {
      setIsLoading(false);
    }
  }, [sessionId, filters]);

  const refreshConversations = useCallback(() => {
    fetchConversations();
  }, [fetchConversations]);

  // Initial fetch
  useEffect(() => {
    fetchConversations();
  }, [fetchConversations]);

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchConversations, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchConversations]);

  const loadConversationsBySession = useCallback(async (newSessionId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await conversationService.getConversationsBySession(newSessionId, 1, 50);
      console.log("Raw conversations response:", response);

      // Filter to show only escalation_req messages and exclude system messages
      const filteredConversations = response.conversations.filter((conv) => {
        console.log("Checking conversation:", conv.message_type, conv.sender_type, conv);

        // Show escalation_req messages
        if (conv.message_type === "escalation_req") {
          console.log("Including escalation_req message:", conv);
          return true;
        }

        // Also show text messages that are not from system
        if (conv.message_type === "TEXT" && conv.sender_type !== "SYSTEM" && (conv.message || conv.payload)) {
          console.log("Including text message:", conv);
          return true;
        }

        console.log("Excluding message:", conv.message_type, conv.sender_type);
        return false;
      });

      console.log("Filtered conversations:", filteredConversations);
      setConversations(filteredConversations);
      setTotalCount(response.total_count);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to load conversations";
      setError(errorMessage);
      console.error("Error loading conversations:", err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearConversations = useCallback(() => {
    setConversations([]);
    setTotalCount(0);
    setError(null);
  }, []);

  return {
    conversations,
    totalCount,
    isLoading,
    error,
    fetchConversations,
    refreshConversations,
    loadConversationsBySession,
    clearConversations,
  };
}

interface ChatHistoryMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
  type: string;
  payload?: Record<string, unknown>;
  meta?: Record<string, unknown>;
  sender_id?: string;
  message_id?: string;
}

export function useChatHistory(sessionId: string) {
  const [chatHistory, setChatHistory] = useState<ChatHistoryMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchChatHistory = useCallback(async () => {
    if (!sessionId) return;

    setIsLoading(true);
    setError(null);

    try {
      const history = await conversationService.getChatHistory(sessionId);
      setChatHistory(history);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch chat history";
      setError(errorMessage);
      console.error("Error fetching chat history:", err);
    } finally {
      setIsLoading(false);
    }
  }, [sessionId]);

  const addMessage = useCallback((message: ChatHistoryMessage) => {
    setChatHistory((prev) => [...prev, message]);
  }, []);

  const updateMessage = useCallback((messageId: string, updates: Partial<ChatHistoryMessage>) => {
    setChatHistory((prev) => prev.map((msg) => (msg.id === messageId ? { ...msg, ...updates } : msg)));
  }, []);

  useEffect(() => {
    fetchChatHistory();
  }, [fetchChatHistory]);

  return {
    chatHistory,
    isLoading,
    error,
    fetchChatHistory,
    addMessage,
    updateMessage,
  };
}

export function useConversationById(id: string) {
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchConversation = useCallback(async () => {
    if (!id) return;

    setIsLoading(true);
    setError(null);

    try {
      const conversationData = await conversationService.getConversationById(id);
      setConversation(conversationData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch conversation";
      setError(errorMessage);
      console.error("Error fetching conversation:", err);
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchConversation();
  }, [fetchConversation]);

  return {
    conversation,
    isLoading,
    error,
    refetch: fetchConversation,
  };
}
