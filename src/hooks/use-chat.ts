'use client';

import { useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export function useChat() {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Initialize socket connection
    const socketInstance = io(process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'http://localhost:3001');

    socketInstance.on('connect', () => {
      console.log('Connected to websocket server');
    });

    socketInstance.on('message', (message: string) => {
      setMessages((prev) => [
        ...prev,
        {
          role: 'assistant',
          content: message,
          timestamp: new Date(),
        },
      ]);
      setIsLoading(false);
    });

    socketInstance.on('disconnect', () => {
      console.log('Disconnected from websocket server');
    });

    setSocket(socketInstance);

    // Cleanup on unmount
    return () => {
      socketInstance.disconnect();
    };
  }, []);

  const sendMessage = (content: string) => {
    if (!socket) return;

    const userMessage: Message = {
      role: 'user',
      content,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setIsLoading(true);

    // Send message to the server
    socket.emit('message', content);
  };

  return {
    messages,
    sendMessage,
    isLoading,
  };
}
