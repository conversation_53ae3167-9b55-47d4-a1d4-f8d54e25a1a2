import { useState, useCallback, useRef, useMemo } from "react";
import { throttle } from "@/lib/utils";

export interface PaginationOptions {
  initialPage?: number;
  pageSize?: number;
}

export interface PaginationState<T> {
  items: T[];
  currentPage: number;
  totalCount: number;
  hasMore: boolean;
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
}

export interface PaginationActions<T> {
  loadInitial: (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => Promise<void>;
  loadMore: (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => Promise<void>;
  refresh: (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => Promise<void>;
  reset: () => void;
  handleScroll: (e: React.UIEvent<HTMLDivElement>, fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>, scrollDirection?: "top" | "bottom") => Promise<void>;
}

export interface UsePaginationReturn<T> extends PaginationState<T>, PaginationActions<T> {}

export function usePagination<T>(options: PaginationOptions = {}): UsePaginationReturn<T> {
  const { initialPage = 1, pageSize = 10 } = options;

  const [state, setState] = useState<PaginationState<T>>({
    items: [],
    currentPage: initialPage,
    totalCount: 0,
    hasMore: true,
    isLoading: false,
    isLoadingMore: false,
    error: null,
  });

  const pageSizeRef = useRef(pageSize);

  const loadInitial = useCallback(
    async (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const response = await fetchFn(initialPage, pageSizeRef.current);
        const totalPages = Math.ceil(response.totalCount / pageSizeRef.current);

        setState({
          items: response.items,
          currentPage: initialPage,
          totalCount: response.totalCount,
          hasMore: initialPage < totalPages,
          isLoading: false,
          isLoadingMore: false,
          error: null,
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to load data";
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        throw error;
      }
    },
    [initialPage]
  );

  const loadMore = useCallback(
    async (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => {
      if (!state.hasMore || state.isLoading || state.isLoadingMore) return;

      setState((prev) => ({ ...prev, isLoadingMore: true, error: null }));

      try {
        const nextPage = state.currentPage + 1;
        const response = await fetchFn(nextPage, pageSizeRef.current);
        const totalPages = Math.ceil(response.totalCount / pageSizeRef.current);

        setState((prev) => ({
          ...prev,
          items: [...prev.items, ...response.items],
          currentPage: nextPage,
          totalCount: response.totalCount,
          hasMore: nextPage < totalPages,
          isLoadingMore: false,
        }));
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to load more data";
        setState((prev) => ({
          ...prev,
          isLoadingMore: false,
          error: errorMessage,
        }));
        throw error;
      }
    },
    [state.hasMore, state.isLoading, state.isLoadingMore, state.currentPage]
  );

  const refresh = useCallback(
    async (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => {
      setState((prev) => ({ ...prev, currentPage: initialPage, hasMore: true }));
      await loadInitial(fetchFn);
    },
    [loadInitial, initialPage]
  );

  const reset = useCallback(() => {
    setState({
      items: [],
      currentPage: initialPage,
      totalCount: 0,
      hasMore: true,
      isLoading: false,
      isLoadingMore: false,
      error: null,
    });
  }, [initialPage]);

  // Create a throttled scroll handler to prevent infinite calls
  const throttledScrollHandler = useMemo(
    () =>
      throttle(
        async (container: HTMLDivElement, fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>, scrollDirection: "top" | "bottom" = "bottom") => {
          // Prevent infinite calls by checking loading states, hasMore, and if we have items
          if (state.isLoading || state.isLoadingMore || !state.hasMore || state.items.length === 0) {
            return;
          }

          const { scrollTop, scrollHeight, clientHeight } = container;

          if (scrollDirection === "bottom") {
            // Load more when scrolled to bottom
            const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100; // 100px threshold
            if (isNearBottom) {
              await loadMore(fetchFn);
            }
          } else {
            // Load more when scrolled to top (for older messages)
            // Add a small threshold to prevent triggering on very small scrolls
            if (scrollTop <= 10) {
              await loadMore(fetchFn);
            }
          }
        },
        300 // 300ms throttle
      ),
    [loadMore, state.isLoading, state.isLoadingMore, state.hasMore, state.items.length]
  );

  const handleScroll = useCallback(
    async (e: React.UIEvent<HTMLDivElement>, fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>, scrollDirection: "top" | "bottom" = "bottom") => {
      const container = e.currentTarget;
      throttledScrollHandler(container, fetchFn, scrollDirection);
    },
    [throttledScrollHandler]
  );

  return {
    ...state,
    loadInitial,
    loadMore,
    refresh,
    reset,
    handleScroll,
  };
}
