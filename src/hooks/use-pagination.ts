import { useState, useCallback, useRef } from "react";

export interface PaginationOptions {
  initialPage?: number;
  pageSize?: number;
}

export interface PaginationState<T> {
  items: T[];
  currentPage: number;
  totalCount: number;
  hasMore: boolean;
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
}

export interface PaginationActions<T> {
  loadInitial: (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => Promise<void>;
  loadMore: (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => Promise<void>;
  refresh: (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => Promise<void>;
  reset: () => void;
}

export interface UsePaginationReturn<T> extends PaginationState<T>, PaginationActions<T> {}

export function usePagination<T>(options: PaginationOptions = {}): UsePaginationReturn<T> {
  const { initialPage = 1, pageSize = 10 } = options;

  const [state, setState] = useState<PaginationState<T>>({
    items: [],
    currentPage: initialPage,
    totalCount: 0,
    hasMore: true,
    isLoading: false,
    isLoadingMore: false,
    error: null,
  });

  const pageSizeRef = useRef(pageSize);

  const loadInitial = useCallback(
    async (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => {
      console.log("SIDEBAR_DEBUG: 📡 Pagination loadInitial starting - page:", initialPage, "pageSize:", pageSizeRef.current);
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const response = await fetchFn(initialPage, pageSizeRef.current);
        const totalPages = Math.ceil(response.totalCount / pageSizeRef.current);

        // Check if we have fewer items than requested page size (indicates end of data)
        const hasMoreByItemCount = response.items.length >= pageSizeRef.current;
        const hasMoreByTotalCount = initialPage < totalPages;

        // We have more data only if both conditions are true
        const hasMore = hasMoreByItemCount && hasMoreByTotalCount;

        console.log(
          `SIDEBAR_DEBUG: ✅ Pagination loadInitial - Page ${initialPage}: received ${response.items.length}/${pageSizeRef.current} items, hasMoreByCount: ${hasMoreByItemCount}, hasMoreByTotal: ${hasMoreByTotalCount}, hasMore: ${hasMore}`
        );

        setState({
          items: response.items,
          currentPage: initialPage,
          totalCount: response.totalCount,
          hasMore,
          isLoading: false,
          isLoadingMore: false,
          error: null,
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to load data";
        console.error("SIDEBAR_DEBUG: ❌ Pagination loadInitial error:", error);
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        throw error;
      }
    },
    [initialPage]
  );

  const loadMore = useCallback(
    async (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => {
      if (!state.hasMore || state.isLoading || state.isLoadingMore) return;

      setState((prev) => ({ ...prev, isLoadingMore: true, error: null }));

      try {
        const nextPage = state.currentPage + 1;
        const response = await fetchFn(nextPage, pageSizeRef.current);
        const totalPages = Math.ceil(response.totalCount / pageSizeRef.current);

        // Check if we have fewer items than requested page size (indicates end of data)
        const hasMoreByItemCount = response.items.length >= pageSizeRef.current;
        const hasMoreByTotalCount = nextPage < totalPages;

        // We have more data only if both conditions are true
        const hasMore = hasMoreByItemCount && hasMoreByTotalCount;

        console.log(
          `Pagination loadMore - Page ${nextPage}: received ${response.items.length}/${pageSizeRef.current} items, hasMoreByCount: ${hasMoreByItemCount}, hasMoreByTotal: ${hasMoreByTotalCount}, hasMore: ${hasMore}`
        );

        setState((prev) => ({
          ...prev,
          items: [...prev.items, ...response.items],
          currentPage: nextPage,
          totalCount: response.totalCount,
          hasMore,
          isLoadingMore: false,
        }));
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to load more data";
        setState((prev) => ({
          ...prev,
          isLoadingMore: false,
          error: errorMessage,
        }));
        throw error;
      }
    },
    [state.hasMore, state.isLoading, state.isLoadingMore, state.currentPage]
  );

  const refresh = useCallback(
    async (fetchFn: (page: number, pageSize: number) => Promise<{ items: T[]; totalCount: number }>) => {
      setState((prev) => ({ ...prev, currentPage: initialPage, hasMore: true }));
      await loadInitial(fetchFn);
    },
    [loadInitial, initialPage]
  );

  const reset = useCallback(() => {
    setState({
      items: [],
      currentPage: initialPage,
      totalCount: 0,
      hasMore: true,
      isLoading: false,
      isLoadingMore: false,
      error: null,
    });
  }, [initialPage]);

  return {
    ...state,
    loadInitial,
    loadMore,
    refresh,
    reset,
  };
}
