"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useOperatorSession } from "./use-operator-session";

let globalWebSocket: WebSocket | null = null;
let connectionCount = 0;

function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

// Define message types
export interface TextMessage {
  type: "text_req";
  sender: "customer" | "operator";
  session_id: string;
  payload: {
    message: string;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface TextResponseMessage {
  type: "text_resp";
  sender: "operator";
  session_id: string;
  payload: {
    message: string;
  };
  meta: {
    message_id: string;
    timestamp: string;
    target_session: string;
  };
}

export interface SessionStartMessage {
  type: "session_start";
  sender: "customer";
  session_id: string;
  payload: {
    isGuest: boolean;
    accessToken?: string;
    idToken?: string;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface ActionMessage {
  type: "action";
  sender?: "customer" | "agent" | "system";
  session_id?: string;
  category: string;
  message?: string; // Optional message to display with the action card
  title?: string; // Title for the action card
  email?: string; // Email for auth actions
  flowType?: "login" | "registration"; // Flow type for auth actions
  data?: unknown; // Data for form actions
  payload?: unknown;
  meta?: {
    message_id: string;
    timestamp: string;
  };
  // Add other fields that might be in action messages
  [key: string]: unknown;
}

export interface SystemMessage {
  type: "system";
  sender: "system";
  session_id: string;
  payload: {
    message: string;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface AgentSuggestionMessage {
  type: "agent_suggestion";
  sender: "agent";
  session_id: string;
  payload: {
    suggestion: string;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface EscalationMessage {
  type: "escalation";
  sender: "customer" | "agent" | "system";
  session_id: string;
  payload: {
    reason: string;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface AuthMessage {
  type: "auth";
  sender: "customer" | "system" | "operator";
  session_id: string;
  payload: {
    isGuest: boolean;
    accessToken?: string;
    idToken?: string;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface ActionResponseMessage {
  type: "action_resp";
  sender: "customer";
  session_id: string;
  payload: {
    action_id: string;
    response: unknown;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface ActionRequestMessage {
  type: "action_req";
  sender: "agent";
  session_id: string;
  payload: {
    action_id: string;
    action_type: string;
    message?: string;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface BubblesMessage {
  type: "bubbles";
  sender: "agent";
  session_id: string;
  payload: {
    bubbles: Array<{
      text: string;
      value?: string;
      [key: string]: unknown;
    }>;
    message?: string;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface SessionStartResponseMessage {
  type: "session_start_resp";
  sender: "agent";
  session_id: string;
  payload: {
    message?: string;
    status: string;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface InitMessage {
  type: "init";
  sender: "system";
  sessionId: string;
  payload: {
    sessionId: string;
  };
  meta: {
    messageId: string;
    createdAt: string;
  };
}

export interface ErrorMessage {
  type: "error";
  sender: "system";
  sessionId: string;
  payload: {
    code: string;
    message: string;
  };
  meta: {
    messageId: string;
    createdAt: string;
  };
}

export type ChatMessage = TextMessage | TextResponseMessage | ActionMessage | ActionRequestMessage | BubblesMessage | SessionStartResponseMessage | SystemMessage | AgentSuggestionMessage;
export type WebSocketMessage =
  | TextMessage
  | TextResponseMessage
  | SessionStartMessage
  | ActionMessage
  | ActionRequestMessage
  | BubblesMessage
  | SessionStartResponseMessage
  | SystemMessage
  | AgentSuggestionMessage
  | EscalationMessage
  | AuthMessage
  | ActionResponseMessage
  | InitMessage
  | ErrorMessage;

interface UseWebSocketOptions {
  escalationSessionId?: string; // When provided, use this session_id for messages and send as text_resp
}

export function useWebSocket(options: UseWebSocketOptions = {}) {
  const { escalationSessionId } = options;
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<Array<ChatMessage & { role: "user" | "assistant"; timestamp: Date }>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionStarted, setSessionStarted] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const socketRef = useRef<WebSocket | null>(null);
  const { sessionId, isLoading: isSessionLoading, isAuthenticated, updateSessionId, updateOperatorId, clearSession, getWebSocketEndpoint } = useOperatorSession();

  // Use refs to store stable function references
  const updateSessionIdRef = useRef(updateSessionId);
  const updateOperatorIdRef = useRef(updateOperatorId);
  const clearSessionRef = useRef(clearSession);

  // Update refs when functions change
  useEffect(() => {
    updateSessionIdRef.current = updateSessionId;
    updateOperatorIdRef.current = updateOperatorId;
    clearSessionRef.current = clearSession;
  }, [updateSessionId, updateOperatorId, clearSession]);

  // Connect to WebSocket
  useEffect(() => {
    // Only connect if user is authenticated and not loading
    if (!isAuthenticated || isSessionLoading) {
      return;
    }

    // Close existing connection if any
    if (socketRef.current) {
      if (socketRef.current.readyState === WebSocket.OPEN || socketRef.current.readyState === WebSocket.CONNECTING) {
        console.log("🔄 Closing existing WebSocket connection before creating new one");
        socketRef.current.close();
      }
      socketRef.current = null;
    }

    if (globalWebSocket && globalWebSocket !== socketRef.current) {
      if (globalWebSocket.readyState === WebSocket.OPEN || globalWebSocket.readyState === WebSocket.CONNECTING) {
        console.log("🔄 Closing existing global WebSocket connection");
        globalWebSocket.close();
      }
      globalWebSocket = null;
    }

    connectionCount++;
    console.log(`🔢 WebSocket connection attempt #${connectionCount}`);

    try {
      // Get WebSocket endpoint from session hook
      const wsUrl = getWebSocketEndpoint();
      console.log(`🔌 Creating NEW WebSocket connection to: ${wsUrl}`);

      const socket = new WebSocket(wsUrl);
      globalWebSocket = socket;

      socket.onopen = () => {
        console.log("✅ WebSocket connection established successfully");
        setIsConnected(true);
        setSessionStarted(false);
        setConnectionError(null);
      };

      socket.onmessage = async (event) => {
        try {
          const rawData = event.data.trim();
          console.log("Raw WebSocket data:", rawData);

          const jsonLines = rawData.split("\n").filter((line: string) => line.trim());
          console.log(`Found ${jsonLines.length} JSON objects in message`);

          for (let i = 0; i < jsonLines.length; i++) {
            const jsonLine = jsonLines[i];
            try {
              const data = JSON.parse(jsonLine);
              console.log(`Processing message ${i + 1}/${jsonLines.length}:`, data);
              await processWebSocketMessage(data);
            } catch (parseError) {
              console.error("Error parsing individual JSON line:", parseError, "Line:", jsonLine);
            }
          }
        } catch (error) {
          console.error("Error processing WebSocket message:", error);
        }
      };

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const processWebSocketMessage = async (data: any) => {
        try {
          // Handle init message - update session ID and send session start
          if (data.type === "init") {
            console.log("Received init message:", data);
            const newSessionId = data.payload?.sessionId;
            if (newSessionId) {
              updateSessionIdRef.current(newSessionId);

              // Send session start after receiving init
              setTimeout(async () => {
                if (!socket || socket.readyState !== WebSocket.OPEN || !isAuthenticated) {
                  console.log("Cannot send session start - missing requirements");
                  return;
                }

                try {
                  // Get authentication tokens from cookies
                  const { getCookie, ACCESS_TOKEN_COOKIE, ID_TOKEN_COOKIE } = await import("@/lib/auth");
                  const accessToken = getCookie(ACCESS_TOKEN_COOKIE);
                  const idToken = getCookie(ID_TOKEN_COOKIE);

                  if (!accessToken || !idToken) {
                    console.error("Missing authentication tokens");
                    setConnectionError("Authentication tokens not found");
                    return;
                  }

                  const sessionStartMessage = {
                    type: "session_start",
                    sender: "operator",
                    sessionId: newSessionId,
                    payload: {
                      is_guest: false,
                      access_token: accessToken,
                      id_token: idToken,
                    },
                  };

                  socket.send(JSON.stringify(sessionStartMessage));
                  console.log("Session start message sent:", sessionStartMessage);
                } catch (error) {
                  console.error("Error sending session start message:", error);
                  setConnectionError("Failed to send session start message");
                }
              }, 100);
            }
            return;
          }

          // Handle session_start_resp - extract and store operator_id
          if (data.type === "session_start_resp") {
            console.log("🎯 Session start response received:", data);

            if (data.payload?.status === "success") {
              console.log("✅ Session start successful, activating chat interface");
              setSessionStarted(true);
              console.log("🚀 Chat interface should now be ready!");

              try {
                let userId = data.payload?.user_id;

                if (!userId) {
                  const { getCookie, ID_TOKEN_COOKIE } = await import("@/lib/auth");
                  const { jwtDecode } = await import("jwt-decode");
                  const idToken = getCookie(ID_TOKEN_COOKIE);

                  if (idToken) {
                    const decoded = jwtDecode<{ user_id?: string; sub?: string }>(idToken);
                    userId = decoded.user_id || decoded.sub;
                  }
                }

                if (userId) {
                  console.log("Setting operator ID:", userId);
                  updateOperatorIdRef.current(userId);
                }
              } catch (error) {
                console.error("Error extracting user_id:", error);
              }
            } else {
              console.error("Session start failed:", data.payload?.message || "Unknown error");
              setConnectionError(data.payload?.message || "Session start failed");
            }
            return;
          }

          // Handle error messages
          if (data.type === "error") {
            console.error("Received error from server:", data);
            const errorMessage = data.payload?.message || "Unknown error occurred";
            setConnectionError(errorMessage);

            // Add error message to chat
            setMessages((prev) => [
              ...prev,
              {
                type: "system",
                sender: "system",
                session_id: "",
                payload: { message: errorMessage },
                meta: { message_id: "", timestamp: "" },
                role: "assistant",
                timestamp: new Date(),
              },
            ]);

            // Close connection on error
            socket.close();
            clearSessionRef.current();
            return;
          }

          if (data.type === "system") {
            console.log("📋 System message received:", data);
            return;
          }

          const isEscalationMode = !!escalationSessionId;

          if (isEscalationMode) {
            if (data.type === "text_req") {
              const messageData = data.message || data;

              if (messageData.session_id === escalationSessionId || data.session_id === escalationSessionId) {
                console.log("📨 Received text_req for escalation:", messageData);
                setMessages((prev) => [
                  ...prev,
                  {
                    ...messageData,
                    role: "user",
                    timestamp: new Date(),
                    content: messageData.message || messageData.content || data.message,
                  },
                ]);
              } else {
                console.log("🚫 text_req not for current escalation:", {
                  messageSessionId: messageData.session_id || data.session_id,
                  currentEscalationSessionId: escalationSessionId,
                });
              }
            } else if (data.type === "text_resp" && data.sender === "operator") {
              console.log("📤 Received operator response:", data);
              setMessages((prev) => [
                ...prev,
                {
                  ...data,
                  role: "assistant",
                  timestamp: new Date(),
                  content: data.message || data.content,
                },
              ]);
            }
          } else {
            if (data.type === "text_resp" || data.type === "action_req" || data.type === "bubbles") {
              setMessages((prev) => [
                ...prev,
                {
                  ...data,
                  role: "assistant",
                  timestamp: new Date(),
                },
              ]);
            }
          }

          setIsLoading(false);
        } catch (error) {
          console.error("Error parsing WebSocket message:", error);
        }
      };

      socket.onclose = () => {
        console.log("❌ WebSocket connection closed");
        setIsConnected(false);
        setSessionStarted(false);
      };

      socket.onerror = (error) => {
        console.error("WebSocket error:", error);
        setConnectionError("WebSocket connection error");
      };

      socketRef.current = socket;

      // Cleanup on unmount
      return () => {
        if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
          socket.close();
        }
      };
    } catch (error) {
      console.error("Error creating WebSocket connection:", error);
      setConnectionError("Failed to create WebSocket connection");
    }
  }, [isAuthenticated, isSessionLoading, getWebSocketEndpoint, escalationSessionId]);

  // Send message function
  const sendMessage = useCallback(
    (message: string) => {
      if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
        console.error("WebSocket is not connected");
        return;
      }

      const isEscalationMode = !!escalationSessionId;

      if (!sessionId) {
        console.error("No operator session ID available for sending message");
        return;
      }

      if (isEscalationMode && !escalationSessionId) {
        console.error("No escalation session ID available for escalation response");
        return;
      }

      const messageObj = isEscalationMode
        ? {
            type: "text_resp" as const,
            sender: "operator" as const,
            session_id: sessionId,
            payload: {
              message: message,
            },
            meta: {
              message_id: generateMessageId(),
              timestamp: getCurrentTimestamp(),
              target_session: escalationSessionId,
            },
          }
        : {
            type: "text_req" as const,
            sender: "operator" as const,
            session_id: sessionId,
            payload: {
              message: message,
            },
            meta: {
              message_id: generateMessageId(),
              timestamp: getCurrentTimestamp(),
            },
          };

      console.log(`Sending ${isEscalationMode ? "escalation response" : "regular message"}:`, messageObj);

      try {
        socketRef.current.send(JSON.stringify(messageObj));

        setMessages((prev) => [
          ...prev,
          {
            ...messageObj,
            role: isEscalationMode ? "assistant" : "user",
            timestamp: new Date(),
          },
        ]);

        if (!isEscalationMode) {
          setIsLoading(true);
        }
      } catch (error) {
        console.error("Error sending message:", error);
      }
    },
    [sessionId, escalationSessionId]
  );

  // Function to clear chat history
  const clearChatHistory = useCallback(() => {
    setMessages([]);
  }, []);

  // Function to manually retry session start (useful for debugging)
  const retrySessionStart = useCallback(() => {
    console.log("Manually retrying session start...");
    setSessionStarted(false);
    // Note: This is for debugging only - in normal flow, session start happens automatically after init
  }, []);

  const isReady = !isSessionLoading && isAuthenticated && sessionStarted;

  useEffect(() => {
    console.log("WebSocket state:", {
      isSessionLoading,
      isAuthenticated,
      sessionStarted,
      isConnected,
      isReady,
    });
  }, [isSessionLoading, isAuthenticated, sessionStarted, isConnected, isReady]);

  return {
    isConnected,
    messages,
    sendMessage,
    isLoading,
    isReady,
    clearChatHistory,
    sessionStarted,
    retrySessionStart,
    connectionError,
  };
}
