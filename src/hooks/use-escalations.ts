import { useState, useEffect, useCallback } from "react";
import { escalationService } from "@/services/escalation-service";
import { useAuth } from "@/hooks/use-auth";
import { Escalation, EscalationListResponse, EscalationFilters, UpdateEscalationRequest } from "@/types/escalation";

// Global event for triggering sidebar refresh
export const SIDEBAR_REFRESH_EVENT = "escalation-sidebar-refresh";

const triggerSidebarRefresh = () => {
  if (typeof window !== "undefined") {
    window.dispatchEvent(new CustomEvent(SIDEBAR_REFRESH_EVENT));
  }
};

interface UseEscalationsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  filters?: EscalationFilters;
}

export function useEscalations(options: UseEscalationsOptions = {}) {
  const { autoRefresh = false, refreshInterval = 30000, filters } = options;
  const { user } = useAuth();

  const [escalations, setEscalations] = useState<Escalation[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchEscalations = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const response: EscalationListResponse = await escalationService.getEscalations(filters);
      setEscalations(response.escalations);
      setTotalCount(response.total_count);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch escalations";
      setError(errorMessage);
      console.error("Error fetching escalations:", err);
    } finally {
      setIsLoading(false);
    }
  }, [user, filters]);

  const fetchMyEscalations = useCallback(async () => {
    if (!user?.id) return;

    setIsLoading(true);
    setError(null);

    try {
      const response: EscalationListResponse = await escalationService.getMyEscalations(user.id, filters);
      setEscalations(response.escalations);
      setTotalCount(response.total_count);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch my escalations";
      setError(errorMessage);
      console.error("Error fetching my escalations:", err);
    } finally {
      setIsLoading(false);
    }
  }, [user, filters]);

  const updateEscalation = useCallback(async (id: string, updates: UpdateEscalationRequest) => {
    try {
      const updatedEscalation = await escalationService.updateEscalation(id, updates);

      // Update the escalation in the local state
      setEscalations((prev) => prev.map((escalation) => (escalation.id === id ? updatedEscalation : escalation)));

      // Trigger sidebar refresh
      triggerSidebarRefresh();

      return updatedEscalation;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update escalation";
      setError(errorMessage);
      console.error("Error updating escalation:", err);
      throw err;
    }
  }, []);

  const assignEscalation = useCallback(
    async (id: string, operatorId?: string) => {
      const assigneeId = operatorId || user?.id;
      if (!assigneeId) {
        throw new Error("No operator ID available for assignment");
      }

      return updateEscalation(id, {
        status: "assigned",
        assigned_operator_id: assigneeId,
      });
    },
    [user, updateEscalation]
  );

  const resolveEscalation = useCallback(
    async (id: string) => {
      if (!user?.id) {
        throw new Error("No user ID available for resolution");
      }

      try {
        // Find the escalation to get its session_id before resolving
        const escalationToResolve = escalations.find((escalation) => escalation.id === id);

        const resolvedEscalation = await escalationService.resolveEscalation(id, user.id);

        // Update the escalation in the local state
        setEscalations((prev) => prev.map((escalation) => (escalation.id === id ? resolvedEscalation : escalation)));

        // Emit custom event to notify session context
        if (typeof window !== "undefined" && escalationToResolve) {
          const event = new CustomEvent("escalation-resolved", {
            detail: {
              escalationId: id,
              sessionId: escalationToResolve.session_id,
              resolvedEscalation,
            },
          });
          window.dispatchEvent(event);
        }

        // Trigger sidebar refresh
        triggerSidebarRefresh();

        return resolvedEscalation;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to resolve escalation";
        setError(errorMessage);
        console.error("Error resolving escalation:", err);
        throw err;
      }
    },
    [user?.id, escalations]
  );

  const refreshEscalations = useCallback(() => {
    fetchEscalations();
  }, [fetchEscalations]);

  // Initial fetch
  useEffect(() => {
    fetchEscalations();
  }, [fetchEscalations]);

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchEscalations, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchEscalations]);

  return {
    escalations,
    totalCount,
    isLoading,
    error,
    fetchEscalations,
    fetchMyEscalations,
    updateEscalation,
    assignEscalation,
    resolveEscalation,
    refreshEscalations,
  };
}

export function useEscalationById(id: string) {
  const [escalation, setEscalation] = useState<Escalation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchEscalation = useCallback(async () => {
    if (!id) return;

    setIsLoading(true);
    setError(null);

    try {
      const escalationData = await escalationService.getEscalationById(id);
      setEscalation(escalationData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch escalation";
      setError(errorMessage);
      console.error("Error fetching escalation:", err);
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchEscalation();
  }, [fetchEscalation]);

  return {
    escalation,
    isLoading,
    error,
    refetch: fetchEscalation,
  };
}
