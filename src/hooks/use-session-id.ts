"use client";

import { useState, useEffect } from "react";
import { jwtDecode } from "jwt-decode";
import { useUuid } from "./use-uuid";

interface IdTokenPayload {
  user_id?: string;
  sub?: string;
  [key: string]: unknown;
}

export function useSessionId() {
  const [sessionId, setSessionId] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const { uuid } = useUuid();

  useEffect(() => {
    const getSessionId = async () => {
      try {
        // Import auth functions
        const { getCookie, ID_TOKEN_COOKIE } = await import("@/lib/auth");
        const idToken = getCookie(ID_TOKEN_COOKIE);

        if (idToken) {
          try {
            // Decode the ID token to get user_id
            const decoded = jwtDecode<IdTokenPayload>(idToken);
            const userId = decoded.user_id || decoded.sub;

            if (userId) {
              setSessionId(userId);
              setIsLoading(false);
              return;
            }
          } catch (error) {
            console.error("Error decoding ID token:", error);
          }
        }

        // Fallback to UUID if no valid user_id found
        if (uuid) {
          setSessionId(uuid);
        }
      } catch (error) {
        console.error("Error getting session ID:", error);
        // Fallback to UUID
        if (uuid) {
          setSessionId(uuid);
        }
      } finally {
        setIsLoading(false);
      }
    };

    getSessionId();
  }, [uuid]);

  return {
    sessionId,
    isLoading,
  };
}
