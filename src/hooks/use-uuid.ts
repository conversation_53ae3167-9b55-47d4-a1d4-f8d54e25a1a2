'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';

// Simple UUID generator function for non-authenticated users
function generateUuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

export function useUuid() {
  const [uuid, setUuid] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    try {
      // For authenticated users, get UUID from user object
      // if (isAuthenticated && user) {
      //   // If user has an ID, use that as the UUID
      //   if (user.id) {
      //     setUuid(user.id);
      //     setIsLoading(false);
      //     return;
      //   }
      // }

      // For non-authenticated users, try to get from localStorage first
      const storedGuestUuid = localStorage.getItem('guest_uuid');

      if (storedGuestUuid) {
        setUuid(storedGuestUuid);
        setIsLoading(false);
        return;
      }

      // If no stored UUID, generate a random one
      // Note: This will be replaced with API call when backend is ready
      const newUuid = generateUuid();

      // Store the UUID for future use
      try {
        localStorage.setItem('guest_uuid', newUuid);
      } catch (e) {
        console.error('Error storing UUID in localStorage', e);
      }

      setUuid(newUuid);
      setIsLoading(false);
    } catch (error) {
      console.error('Error in useUuid hook:', error);
      // Fallback to a new UUID if everything else fails
      setUuid(generateUuid());
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  return { uuid, isLoading };
}
