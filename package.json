{"name": "k2-operator-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "prepare": "husky install"}, "dependencies": {"@hookform/resolvers": "^3.3.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-tabs": "^1.1.11", "axios": "^1.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^3.0.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.503.0", "next": "15.3.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.0", "socket.io-client": "^4.7.2", "tailwind-merge": "^2.0.0", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^15.0.0", "@testing-library/user-event": "^14.5.0", "@types/node": "^20", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "eslint": "^9", "eslint-config-next": "15.3.1", "husky": "^9.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "tailwindcss": "^4", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}