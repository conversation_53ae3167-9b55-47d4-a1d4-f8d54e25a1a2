version: '3.8'

services:
  k2-customer-app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      # Runtime environment variables - matching .env.local
      - NEXT_PUBLIC_AUTH_API_BASE_URL=${NEXT_PUBLIC_AUTH_API_BASE_URL:-http://127.0.0.1:3002}
      - NEXT_PUBLIC_WEBSOCKET_URL=${NEXT_PUBLIC_WEBSOCKET_URL:-wss://k2-api.klubworks.com/ws/}
      - NEXT_PUBLIC_SERVICES_BASE_URL=${NEXT_PUBLIC_SERVICES_BASE_URL:-http://localhost:8080}
      - NEXT_PUBLIC_AUTH_SERVICE_URL=${NEXT_PUBLIC_AUTH_SERVICE_URL:-http://127.0.0.1:3002/auth-svc/api/v1}
      - NEXT_PUBLIC_OAUTH_CLIENT_ID=${NEXT_PUBLIC_OAUTH_CLIENT_ID:-test-operator-app-client}
      - NEXT_PUBLIC_OAUTH_REDIRECT_URL=${NEXT_PUBLIC_OAUTH_REDIRECT_URL:-http://127.0.0.1:3000/login}
      # Server-side environment variables
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "node", "-e", "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1); })" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
